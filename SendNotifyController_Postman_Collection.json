{"info": {"name": "SendNotifyController API Collection", "description": "Collection of API endpoints for the SendNotifyController in the Integration Hub application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Send Notification for Van Ban", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/sendnoti/vanban?status=INITIAL", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "query": [{"key": "status", "value": "INITIAL", "description": "Status of the van ban documents to send notifications for (e.g., INITIAL, FAIL)"}]}, "description": "Manually trigger the sendNotificationForVanBan function to send notifications for documents with the specified status."}, "response": []}, {"name": "Send Notification for Late Documents", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/sendnoti/vanbantre", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "description": "Manually trigger the runNotifySendOneDayLate function to send notifications for documents that are one day late."}, "response": []}, {"name": "Send Function Call Count Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/sendnoti/functioncalls", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "functioncalls"]}, "description": "Manually trigger the sendFunctionCallCountNotification function to send notifications about function call counts."}, "response": []}, {"name": "Send One-Hour Statistics Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/sendnoti/onehour", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "onehour"]}, "description": "Manually trigger the runNotifyGuiNhan1Hour function to send notifications about documents processed in the last hour."}, "response": []}, {"name": "Increment Function Call Counter", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/sendnoti/increment/SEND", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON>", "increment", "SEND"]}, "description": "Manually increment the function call counter for the SEND function type. Replace SEND with GET or UPDATE to increment those counters instead."}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:8018", "type": "string", "description": "Base URL for the Integration Hub API"}]}