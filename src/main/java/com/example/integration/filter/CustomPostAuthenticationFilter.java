package com.example.integration.filter;

import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.constant.NotificationConstant;
import com.example.integration.hub.service.CacheService;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.MessageUtil;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.fds.flex.context.model.User;

import com.fds.flex.user.context.UserContextHolder;
import com.fds.flex.user.context.UserContextImpl;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
@Slf4j
public class CustomPostAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private CacheService cacheService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        setUserContext(null);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null && authentication.isAuthenticated()
                && !authentication.getPrincipal().toString().equals("anonymousUser")) {

            JSONObject principalObj = new JSONObject(authentication.getPrincipal());

            JSONObject claims = principalObj.optJSONObject("claims");

            if (Validator.isNull(claims)) {
                log.info("Token khong co thong tin claims");
                JSONObject message = new JSONObject();
                message.put("message", MessageUtil.getVietNameseMessageFromResourceBundle(Constant.LANGUAGE,
                        NotificationConstant.USER_HAS_NOT_PERMISSION_TO_DO_THIS_ACTION));
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.getWriter().write(message.toString());
                return;
            }
        String typeSSO = GetterUtil.get(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_SSO_SYSTEM),
                StringPool.BLANK);
            if (Validator.isNotNull(typeSSO) && claims.getString("iss").contains(typeSSO)) {

                User user = cacheService.getUser(claims.getString("sub"), claims.getString("sub"));

                if (Validator.isNull(user)) {
                    log.info("Khong tim thay thong tin danh tinh dien tu");
                    JSONObject message = new JSONObject();
                    message.put("message", MessageUtil.getVietNameseMessageFromResourceBundle("language",
                            NotificationConstant.USER_HAS_NOT_PERMISSION_TO_DO_THIS_ACTION));
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write(message.toString());
                    return;
                }

                setUserContext(user);
            }
            else  {
                User user = cacheService.getUser(claims.getString("jti"), claims.getString("sub"));

                if (Validator.isNull(user)) {
                    log.info("Khong tim thay thong tin danh tinh dien tu");
                    JSONObject message = new JSONObject();
                    message.put("message", MessageUtil.getVietNameseMessageFromResourceBundle("language",
                            NotificationConstant.USER_HAS_NOT_PERMISSION_TO_DO_THIS_ACTION));
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write(message.toString());
                    return;
                }

                setUserContext(user);
            }


        }

        filterChain.doFilter(request, response);
    }

    private void setUserContext(User user) {
        UserContextImpl userContext = new UserContextImpl.Builder().setUser(user)
                .setContextTime(System.currentTimeMillis()).build();

        UserContextHolder.setStrategyName(UserContextHolder.MODE_THREADLOCAL);

        UserContextHolder.setContext(userContext);
    }
}
