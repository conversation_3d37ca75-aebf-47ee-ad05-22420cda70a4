package com.example.integration.hub.scheduler;

import com.example.integration.hub.service.impl.SchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SendScheduler {
    @Autowired
    SchedulerService service;

    @Value("${integration.hub.lgsp.job.sendEdoc.enabled}")
    private boolean isSchedulerEnabled;

    @Scheduled(fixedDelayString = "${integration.hub.lgsp.job.sendEdoc.time}")
    public void run() {

        if(!isSchedulerEnabled) {
            return;
        }

        log.info("====Start job send EDOC...");
        int edocReceived = service.startSendEdoc();
        log.info("====Done send " + edocReceived + " EDOC!");
    }

}
