//package com.example.integration.hub.scheduler;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.Trigger;
//import org.springframework.scheduling.annotation.SchedulingConfigurer;
//import org.springframework.scheduling.config.ScheduledTaskRegistrar;
//import org.springframework.scheduling.support.CronTrigger;
//
//import java.util.concurrent.atomic.AtomicReference;
//
//@Configuration
//public class DynamicSchedulerConfig implements SchedulingConfigurer {
//
//    @Autowired
//    GetAgencyScheduler getAgencyScheduler;
//
//    private final AtomicReference<String> cronExpression = new AtomicReference<>("0 0 11 * * *"); // mặc định
//
//
//    public void updateCronExpression(String newCron) {
//        this.cronExpression.set(newCron);
//    }
//
//    @Override
//    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
//        Runnable task = () -> getAgencyScheduler.scheduleGetListAgencies();
//
//        Trigger trigger = triggerContext -> {
//            String cron = cronExpression.get(); // Lấy từ biến động
//            return new CronTrigger(cron).nextExecutionTime(triggerContext);
//        };
//
//        taskRegistrar.addTriggerTask(task, trigger);
//
//    }
//}
//
//
