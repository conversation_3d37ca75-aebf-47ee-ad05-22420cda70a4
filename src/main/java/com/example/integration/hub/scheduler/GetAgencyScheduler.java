//package com.example.integration.hub.scheduler;
//
//import com.example.integration.config.PropKey;
//import com.example.integration.hub.action.SDKVXPAction;
//import com.example.integration.hub.action.SendNotifiAction;
//import com.example.integration.hub.action.impl.SDKVXPActionImpl;
//import com.example.integration.hub.service.impl.HangDoiGoiTinServiceImpl;
//import com.example.integration.hub.service.impl.SchedulerService;
//import com.example.integration.hub.util.FunctionCallCounter;
//import com.fds.flex.common.ultility.GetterUtil;
//import com.fds.flex.common.utility.string.StringPool;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//
//@Slf4j
//@Service
//@ConditionalOnProperty(value = "app.scheduler.enabled", havingValue = "true", matchIfMissing = true)
//
//public class GetAgencyScheduler {
//    @Autowired
//    private SchedulerService service;
//
//
//    @Autowired
//    private FunctionCallCounter functionCallCounter;
////    @Scheduled(cron = "0 0 7 * * *")
//    public void scheduleGetListAgencies() {
//
//        String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
//        String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
//        String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);
//
//        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
//        log.info("Bắt đầu đồng bộ danh sách đơn vị liên thông");
//        try {
//            service.getListAgencies(sdkVxpAction);
//            log.info("Kết thúc đồng bộ danh sách đơn vị liên thông");
//        } catch (Exception e) {
//            log.error("Lỗi khi đồng bộ danh sách đơn vị liên thông: {}", e.getMessage());
//        }
//    }
//}
//
//
