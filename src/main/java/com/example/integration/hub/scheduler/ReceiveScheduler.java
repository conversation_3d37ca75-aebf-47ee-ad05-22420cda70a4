package com.example.integration.hub.scheduler;

import com.example.integration.hub.service.impl.SchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ReceiveScheduler {
    @Autowired
    SchedulerService service;

    @Value("${integration.hub.lgsp.job.receiveEdoc.enabled}")
    private boolean isSchedulerEnabled;

    @Scheduled(fixedDelayString = "${integration.hub.lgsp.job.receiveEdoc.time}")
    public void run() {
        if(!isSchedulerEnabled) {
            return;
        }

        log.info("====Start job receive EDOC...");
        int edocReceived = service.startReceiveEdoc();
        log.info("====Done receive " + edocReceived + " EDOC!");
    }
}
