package com.example.integration.hub.scheduler;

import com.example.integration.constant.Constant;
import com.example.integration.hub.service.impl.SchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DynamicScheduler {
    @Autowired
    SchedulerService service;

    @Value("${integration.hub.lgsp.job.notifyGuiNhan1Hour.enabled:false}")
    private boolean isNotifyGuiNhan1Hour;

    @Value("${integration.hub.lgsp.job.notifyVanbanton.enabled:false}")
    private boolean isNotifyVanbantonEnabled;

    @Value("${integration.hub.lgsp.job.notifyVanbanloi.enabled:false}")
    private boolean isNotifyVanbanloiEnabled;

    @Value("${integration.hub.lgsp.job.notifySendOneDayLate.enabled:false}")
    private boolean isNotifySendOneDayLateEnabled;

    @Value("${integration.hub.lgsp.job.notifyFunctionCalls.enabled:false}")
    private boolean isNotifyFunctionCallsEnabled;


    @Scheduled(fixedDelayString = "${integration.hub.lgsp.job.notifyGuiNhan1Hour.time}")
    public void runNotifyGuiNhan1Hour() {
        if(!isNotifyGuiNhan1Hour) {
            return;
        }

        log.info("====Start job dynamic...");
        service.runNotifyGuiNhan1Hour();
    }

    @Scheduled(fixedDelayString = "${integration.hub.lgsp.job.notifyVanbanton.time}")
    public void runNotifyVanBanTon() {
        if(!isNotifyVanbantonEnabled) {
            return;
        }

        log.info("====Start job notify for van ban...");
        service.sendNotificationForVanBan(Constant.STATUS_INITIAL);
    }


    @Scheduled(fixedDelayString = "${integration.hub.lgsp.job.notifyVanbanloi.time}")
    public void runNotifyVanBanLoi() {
        if(!isNotifyVanbanloiEnabled) {
            return;
        }

        log.info("====Start job notify for van ban loi...");
        service.sendNotificationForVanBan(Constant.STATUS_FAIL);
    }

    @Scheduled(fixedDelayString = "${integration.hub.lgsp.job.notifySendOneDayLate.time:86400000}")
    public void runNotifySendOneDayLate() {
        if(!isNotifySendOneDayLateEnabled) {
            return;
        }

        log.info("====Start job notify for send documents that are one day late...");
        service.runNotifySendOneDayLate();
    }

    /**
     * Scheduler that runs at 23:00 every day to send notifications about function call counts
     * and reset the counts
     */
    @Scheduled(cron = "0 0 23 * * ?")
    public void runNotifyFunctionCalls() {
        if(!isNotifyFunctionCallsEnabled) {
            return;
        }

        log.info("====Start job notify for function call counts...");
        service.sendFunctionCallCountNotification();
    }
}
