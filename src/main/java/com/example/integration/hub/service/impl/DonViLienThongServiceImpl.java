package com.example.integration.hub.service.impl;

import com.example.integration.constant.Constant;
import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.hub.service.DonViLienThongService;
import com.example.integration.repository.DonViLienThongRepository;
import com.fds.flex.common.ultility.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DonViLienThongServiceImpl implements DonViLienThongService {
    @Autowired
    private DonViLienThongRepository repository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public C_DonViLienThong update(C_DonViLienThong object) {
        return repository.save(object);
    }

    @Override
    public void delete(C_DonViLienThong object) {
        repository.delete(object);
    }

    @Override
    public Optional<C_DonViLienThong> findById(String id) {
        return repository.findById(id);
    }

    @Override
    public List<C_DonViLienThong> filter() {
        return repository.findAll();
    }

    @Override
    public Page<C_DonViLienThong> filterSecondLevel(String keyword, Pageable pageable, String tenMuc, String maMuc, Long tuNgay, Long denNgay,String id,String ma) {
        Query query = new Query().with(pageable);

        List<Criteria> criteria = new ArrayList<>();

        if (Validator.isNotNull(keyword)) {

            List<Criteria> subCriterias = new ArrayList<>();
            Criteria c = Criteria.where("MaMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            c = Criteria.where("TenMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);

            criteria.add(new Criteria().orOperator(subCriterias));
        }
        if (Validator.isNotNull(tenMuc)) {
            Criteria c = Criteria.where("TenMuc").is(tenMuc);
            criteria.add(c);
        }
        if (Validator.isNotNull(maMuc)) {
            Criteria c = Criteria.where("MaMuc").is(maMuc);
            criteria.add(c);
        }
        if (Validator.isNotNull(tuNgay)) {
            Criteria c = Criteria.where("ThoiGianTao").gte(tuNgay);
            criteria.add(c);
        }
        if (Validator.isNotNull(denNgay)) {
            Criteria c = Criteria.where("ThoiGianTao").lte(denNgay);
            criteria.add(c);
        }
        if (Validator.isNotNull(ma)) {
            Criteria c = Criteria.where("Ma").is(ma);
            criteria.add(c);
        }
        if (Validator.isNotNull(id)) {
            Criteria c = Criteria.where("pid").is(id);
            criteria.add(c);
        }
        if (!criteria.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteria));
        }

        return PageableExecutionUtils.getPage(mongoTemplate.find(query, C_DonViLienThong.class), pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), C_DonViLienThong.class));

    }


    @Override
    public Optional<C_DonViLienThong> findByMaMuc(String maDinhDanh,String trangThai_MaMuc) {
        return repository.findByMaMuc(maDinhDanh,trangThai_MaMuc);
    }
    @Override
    public List<C_DonViLienThong> findByMaMucNotIn(List<String> maMucList) {
        return repository.findByMaMucNotIn(maMucList);
    }

    @Override
    public List<C_DonViLienThong> findAll() {
        return repository.findAll();
    }

    @Override
    public List<C_DonViLienThong> findByMaMucIn(List<String> maMucList,String trangThai) {
        return repository.findByMaMucIn(maMucList,trangThai);
    }

    @Override
    public void saveAll(List<C_DonViLienThong> entities) {
        repository.saveAll(entities);
    }


    private String toLikeKeyword(String source) {
        return source.replaceAll("\\*", ".*");
    }

}
