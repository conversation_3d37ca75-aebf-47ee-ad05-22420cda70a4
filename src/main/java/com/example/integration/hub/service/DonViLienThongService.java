package com.example.integration.hub.service;

import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.entity.C_DonViLienThong;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;


public interface DonViLienThongService {
    C_DonViLienThong update(C_DonViLienThong object);

    void delete(C_DonViLienThong object);

    Optional<C_DonViLienThong> findById(String id);

    List<C_DonViLienThong> filter();
    Page<C_DonViLienThong> filterSecondLevel(String keyword, Pageable pageable, String tenDonViLienThong, String maDonViLienThong, Long tuNgay, Long denNgay,String id,String ma);

    List<C_DonViLienThong> findByMaMucNotIn(List<String> maMucList);
    Optional<C_DonViLienThong> findByMaMuc(String maDinhDanh,String trangThai_MaMuc);

    List<C_DonViLienThong> findAll();
    List<C_DonViLienThong> findByMaMucIn(List<String> maMucList,String trangThai);
    void saveAll(List<C_DonViLienThong> entities);

}
