package com.example.integration.hub.service;

import com.example.integration.entity.HangDoiGoiTin;
import com.example.integration.entity.MonthlySummary;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.entity.ThongKeLienThongVanBan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface HangDoiGoiTinService {

	long countAll();

	Optional<HangDoiGoiTin> findById(String id);

//	Optional<HangDoiGoiTin> findBy

	Optional<HangDoiGoiTin> findByMaGoiTin(String maGoiTin);

	void delete(HangDoiGoiTin object);

	HangDoiGoiTin update(HangDoiGoiTin object);

	Map<String, HangDoiGoiTin> update(Map<String, HangDoiGoiTin> map);

	Page<HangDoiGoiTin> filter(String keyword, String trucTichHop, String trangthai,  String noiGui, String noiNhan, String dinhDangGoiTin, String kieuLoaiGoiTin,String trangThaiLienThong_MaMuc,String tieuDe, String soKyHieu,String loaiGoiTin,String trangThaiVanBan,String kieuVanBan,Long tuNgay,Long denNgay,Pageable pageable);

	Page<HangDoiGoiTin> filter(String trucTichHop, String kieuLoaiGoiTin, String trangThai, Long tuNgay, Long denNgay, String orderFields,  String orderTypes, Pageable pageable);


	Page<HangDoiGoiTin> filterForScheduler(String kieuLoaiGoiTin, String trangThai, String giaoThucKetNoi, Pageable pageable);

	List<HangDoiGoiTin> findByKieuLoaiGoiTinAndMaPhienBanAndTrangThai(String kieuLoaiGoiTin, String maPhienBan, String trangThai);

	List<HangDoiGoiTin> findByKieuLoaiGoiTinAndThoiGianGui(String kieuLoaiGoiTin, long thoiGianGui);

	List<HangDoiGoiTin> findByKieuLoaiGoiTinAndTrangThaiAndThoiGianGuiLessThan(String kieuLoaiGoiTin, String trangThai, long thoiGianGui);

	List<MonthlySummary> getSummaryByYearAndKieuLoaiGoiTin (String kieuLoaiGoiTin, int year);

	ThongKeLienThongVanBan.TiLeVanBan thongKeTiLeVanBanNhan(int year);
	ThongKeLienThongVanBan.TiLeVanBan thongKeTiLeVanBanGui(int year);
}
