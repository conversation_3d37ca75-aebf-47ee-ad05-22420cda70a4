package com.example.integration.hub.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;


import com.example.integration.entity.TrucTichHop;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TrucTichHopService {
	
	long countAll();

	Optional<TrucTichHop> findById(String id);

	Optional<TrucTichHop> findByHeThongKetNoi_MaKetNoiAndMaMuc(String maKetNoi, String maTruc, String kieuLoaiGoiTin);

	void delete(TrucTichHop object);

	TrucTichHop update(TrucTichHop object);

	Map<String, TrucTichHop> update(Map<String, TrucTichHop> map);

	Page<TrucTichHop> filter(String keyword,Long tuNgay,Long denNgay, Pageable pageable);

	public List<TrucTichHop> findAll();
}
