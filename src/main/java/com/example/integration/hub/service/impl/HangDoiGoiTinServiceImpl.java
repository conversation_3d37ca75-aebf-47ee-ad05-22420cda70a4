package com.example.integration.hub.service.impl;

import com.example.integration.constant.Constant;
import com.example.integration.entity.*;
import com.example.integration.hub.service.HangDoiGoiTinService;
import com.example.integration.repository.HangDoiGoiTinRepository;
import com.fds.flex.common.ultility.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class HangDoiGoiTinServiceImpl implements HangDoiGoiTinService {
    @Autowired
    private HangDoiGoiTinRepository repository;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public long countAll() {
        return repository.count();
    }

    @Override
    public Optional<HangDoiGoiTin> findById(String id) {
        return repository.findById(id);
    }

    @Override
    public Optional<HangDoiGoiTin> findByMaGoiTin(String maGoiTin) {
        return repository.findByMaGoiTin(maGoiTin);
    }

    @Override
    public void delete(HangDoiGoiTin object) {
        repository.delete(object);
    }

    @Override
    public HangDoiGoiTin update(HangDoiGoiTin object) {
        return repository.save(object);
    }

    @Override
    public Map<String, HangDoiGoiTin> update(Map<String, HangDoiGoiTin> map) {
        map.forEach((k, v) -> {
            {
                repository.save(v);
            }
        });
        return map;
    }

    @Override
    public Page<HangDoiGoiTin> filter(String keyword, String trucTichHop, String trangthai, String noiGui, String noiNhan, String dinhDangGoiTin ,
                                      String kieuLoaiGoiTin,String trangThaiLienThong_MaMuc,String tieuDe, String soKyHieu,String loaiGoiTin,String trangThaiVanBan,String kieuVanBan,Long tuNgay,Long denNgay,
                                      Pageable pageable) {
        Query query = new Query().with(pageable);

        List<Criteria> criteria = new ArrayList<>();

        if (Validator.isNotNull(keyword)) {
            List<Criteria> subCriterias = new ArrayList<>();
            Criteria c = Criteria.where("MaGoiTin").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            c = Criteria.where("KieuLoaiGoiTin").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            c = Criteria.where("TenKieuLoaiGoiTin").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            criteria.add(new Criteria().orOperator(subCriterias));
        }


        if (Validator.isNotNull(dinhDangGoiTin)) {
            Criteria c = Criteria.where("DinhDangGoiTin").is(dinhDangGoiTin);
            criteria.add(c);
        }

        if (Validator.isNotNull(kieuLoaiGoiTin)) {
            Criteria c = Criteria.where("KieuLoaiGoiTin").is(kieuLoaiGoiTin);
            criteria.add(c);
        }
        if (Validator.isNotNull(tieuDe)) {
            Criteria c = Criteria.where("ContentEdoc.tieuDe").is(tieuDe);
            criteria.add(c);
        }
        if (Validator.isNotNull(soKyHieu)) {
            Criteria c = Criteria.where("ContentEdoc.soKyHieu").is(soKyHieu);
            criteria.add(c);
        }
        if(Validator.isNotNull(loaiGoiTin)) {
            Criteria c = Criteria.where("ContentEdoc.loaiGoiTin").is(loaiGoiTin);
            criteria.add(c);
        }
        if(Validator.isNotNull(trangThaiVanBan)) {
            Criteria c = Criteria.where("ContentEdoc.trangThaiVanBan").regex(toLikeKeyword(trangThaiVanBan));
            criteria.add(c);
        }
        if(Validator.isNotNull(kieuVanBan)) {
            Criteria c = Criteria.where("ContentEdoc.kieuVanBan").regex(toLikeKeyword(kieuVanBan));
            criteria.add(c);
        }
        if (Validator.isNotNull(trucTichHop)) {
            Criteria c = Criteria.where("TrucTichHop.MaMuc").is(trucTichHop);
            criteria.add(c);
        }
        if (Validator.isNotNull(trangThaiLienThong_MaMuc)) {
            Criteria c = Criteria.where("TrangThaiLienThong.MaMuc").is(trangThaiLienThong_MaMuc);
            criteria.add(c);
        }

        if (Validator.isNotNull(trangthai)) {
            Criteria c = Criteria.where("NoiNhanGoiTin.TrangThaiLienThong.MaMuc").is(trangthai); //Goi tin chua dc xu ly
            criteria.add(c);
        }

        if (Validator.isNotNull(noiNhan)) {
            Criteria c = Criteria.where("NoiNhanGoiTin.MaKetNoi").is(noiNhan);
            criteria.add(c);
        }

        if (Validator.isNotNull(noiGui)) {
            Criteria c = Criteria.where("NoiGuiGoiTin.MaKetNoi").is(noiGui);
            criteria.add(c);
        }
        if (Validator.isNotNull(tuNgay) && tuNgay > 0) {
            Criteria c = Criteria.where("NoiGuiGoiTin.ThoiGianGui").gte(tuNgay);
            criteria.add(c);
        }

        if (Validator.isNotNull(denNgay) && denNgay > 0) {
            Criteria c = Criteria.where("NoiGuiGoiTin.ThoiGianGui").lte(denNgay);
            criteria.add(c);
        }
        if (!criteria.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteria));
            query.with(Sort.by(Sort.Direction.DESC, "NoiGuiGoiTin.ThoiGianGui"));
        }

        return PageableExecutionUtils.getPage(mongoTemplate.find(query, HangDoiGoiTin.class), pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), HangDoiGoiTin.class));
    }

    @Override
    public Page<HangDoiGoiTin> filter(String trucTichHop, String kieuLoaiGoiTin,String trangThai, Long tuNgay, Long denNgay, String orderFields,  String orderTypes, Pageable pageable) {
        Query query = new Query().with(pageable);

        List<Criteria> criteria = new ArrayList<>();

        if (Validator.isNotNull(kieuLoaiGoiTin)) {
            Criteria c = Criteria.where("KieuLoaiGoiTin").is(kieuLoaiGoiTin);
            criteria.add(c);
        }

        if (Validator.isNotNull(trucTichHop)) {
            Criteria c = Criteria.where("TrucTichHop.MaMuc").is(trucTichHop);
            criteria.add(c);
        }

        if (Validator.isNotNull(trangThai)) {
            Criteria c = Criteria.where("NoiNhanGoiTin.TrangThaiLienThong.MaMuc").is(trangThai);
            criteria.add(c);
        }

        if (Validator.isNotNull(tuNgay) && tuNgay > 0) {
            Criteria c = Criteria.where("NoiNhanGoiTin.ThoiGianKetNoi").gte(tuNgay);
            criteria.add(c);
        }

        if (Validator.isNotNull(denNgay) && denNgay > 0) {
            Criteria c = Criteria.where("NoiNhanGoiTin.ThoiGianKetNoi").lte(denNgay);
            criteria.add(c);
        }

        Criteria c = Criteria.where("PhanVungDuLieu.MaMuc").ne(Constant.DA_DOC);
        criteria.add(c);

        if (!criteria.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteria));
            query.with(Sort.by(Sort.Direction.DESC, orderFields));
        }

        return PageableExecutionUtils.getPage(mongoTemplate.find(query, HangDoiGoiTin.class), pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), HangDoiGoiTin.class));
    }

    @Override
    public Page<HangDoiGoiTin> filterForScheduler(String kieuLoaiGoiTin, String trangThai, String giaoThucKetNoi, Pageable pageable) {
        Query query = new Query().with(pageable);

        List<Criteria> predicates = new ArrayList<>();


        if (Validator.isNotNull(kieuLoaiGoiTin)) {
            Criteria c = Criteria.where("KieuLoaiGoiTin").is(kieuLoaiGoiTin);
            predicates.add(c);
        }

        // 2) Điều kiện nằm trong cùng 1 phần tử của NoiNhanGoiTin
        Criteria elem = new Criteria();       // khung criteria cho elemMatch
        boolean hasElemCondition = false;

        if (Validator.isNotNull(trangThai)) {
            elem = elem.and("TrangThaiLienThong.MaMuc").is(trangThai);
            hasElemCondition = true;
        }

        if (Validator.isNotNull(giaoThucKetNoi)) {
            elem = elem.and("GiaoThucKetNoi.MaMuc").is(giaoThucKetNoi);
            hasElemCondition = true;
        }

        if (hasElemCondition) {
            predicates.add(Criteria.where("NoiNhanGoiTin").elemMatch(elem));
        }

        if (!predicates.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(predicates));
            query.with(Sort.by(Sort.Direction.DESC, "NoiGuiGoiTin.ThoiGianGui"));
        }

        return PageableExecutionUtils.getPage(mongoTemplate.find(query, HangDoiGoiTin.class), pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), HangDoiGoiTin.class));
    }

    @Override
    public List<HangDoiGoiTin> findByKieuLoaiGoiTinAndMaPhienBanAndTrangThai(String kieuLoaiGoiTin, String maPhienBan, String trangThai) {
        return repository.findByKieuLoaiGoiTinAndMaPhienBanAndTrangThai(kieuLoaiGoiTin, maPhienBan, trangThai);
    }

    @Override
    public List<HangDoiGoiTin> findByKieuLoaiGoiTinAndThoiGianGui(String kieuLoaiGoiTin, long thoiGianGui) {
        return repository.findByKieuLoaiGoiTinAndThoiGianGui(kieuLoaiGoiTin, thoiGianGui);
    }

    @Override
    public List<HangDoiGoiTin> findByKieuLoaiGoiTinAndTrangThaiAndThoiGianGuiLessThan(String kieuLoaiGoiTin, String trangThai, long thoiGianGui) {
        return repository.findByKieuLoaiGoiTinAndTrangThaiAndThoiGianGuiLessThan(kieuLoaiGoiTin, trangThai, thoiGianGui);
    }

    @Override
    public List<MonthlySummary> getSummaryByYearAndKieuLoaiGoiTin(String kieuLoaiGoiTin, int year) {
        return repository.getSummaryByYearAndKieuLoaiGoiTin(kieuLoaiGoiTin, year);
    }

    @Override
    public ThongKeLienThongVanBan.TiLeVanBan thongKeTiLeVanBanNhan(int year) {
        ThongKeLienThongVanBan.TiLeVanBan result = repository.thongKeTiLeVanBanNhan(year);
        if (Validator.isNotNull(result))
            return result;
        return new ThongKeLienThongVanBan.TiLeVanBan();
    }

    @Override
    public ThongKeLienThongVanBan.TiLeVanBan thongKeTiLeVanBanGui(int year) {
		ThongKeLienThongVanBan.TiLeVanBan result = repository.thongKeTiLeVanBanGui(year);
		if (Validator.isNotNull(result))
			return result;
		return new ThongKeLienThongVanBan.TiLeVanBan();
    }

    private String toLikeKeyword(String source) {
        return source.replaceAll("\\*", ".*");
    }

}
