package com.example.integration.hub.service.impl;

import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.entity.MonthlySummary;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.entity.ext.LoaiNguonLuuTruExt;
import com.example.integration.hub.service.TepDuLieuService;
import com.example.integration.repository.TepDuLieuRepository;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.FileSystemResource;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Slf4j
@Service
public class TepDuLieuServiceImpl implements TepDuLieuService {
	@Autowired
	private TepDuLieuRepository repository;

	@Autowired
	private MongoTemplate mongoTemplate;

	@Override
	public Optional<TepDuLieu> findByMaDinhDanh(String maDinhDanh) {
		return repository.findByMaDinhDanh(maDinhDanh);
	}

	@Override
	public Optional<TepDuLieu> findById(String id) {
		return repository.findById(id);
	}

	@Override
	public Optional<TepDuLieu> findByDuongDanURL(String duongDanURL) {
		return repository.findByDuongDanURL(duongDanURL);
	}

	@Override
	public void deleteTepDuLieu(TepDuLieu tepDuLieu) {
		repository.delete(tepDuLieu);
	}

	@Override
	public TepDuLieu updateTepDuLieu(TepDuLieu tepDuLieu) {
		return repository.save(tepDuLieu);
	}

	@Override
	public TepDuLieu uploadFile(MultipartFile file, String loaiNguonLuuTru,String messagetype) {
		try {
			String fileName = file.getOriginalFilename();
			String uploadDir = PortalUtil.remakeStaticResourceDir(
					GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_DIR)));

			// Create the full path to save the file
			Path filePath = Paths.get(uploadDir).toAbsolutePath().normalize();
			File uploadDirectory = new File(filePath.toString());

			// Create directory if it doesn't exist
			if (!uploadDirectory.exists()) {
				uploadDirectory.mkdirs();
			}
			String maDinhDanh = UUID.randomUUID().toString();
			int lastDotIndex = fileName.lastIndexOf('.');
			String urlSave = filePath + File.separator + maDinhDanh  + "." + fileName.substring(lastDotIndex + 1);
			File destinationFile = new File(urlSave);

			// Save the file
			file.transferTo(destinationFile);

			TepDuLieu tepDuLieu = new TepDuLieu();
			tepDuLieu.setMaDinhDanh(maDinhDanh);
			tepDuLieu.setTenTep(fileName);
			tepDuLieu.setDuongDanURL(urlSave);
			tepDuLieu.setDinhDangTep("edxml");
			tepDuLieu.setDinhDangGoiTin(messagetype);
			tepDuLieu.setKichThuocTep(file.getSize());
			LoaiNguonLuuTruExt loaiNguonLuuTruExt = new LoaiNguonLuuTruExt();
			loaiNguonLuuTruExt.setMaMuc(loaiNguonLuuTru);
			tepDuLieu.setLoaiNguonLuuTru(loaiNguonLuuTruExt);
			return repository.save(tepDuLieu);
		} catch (Exception e) {
			log.error("Error when upload multipartFile: ", e);
		}
		return null;
	}

	@Override
	public ResponseEntity<Resource> downloadFile(String maDinhDanh) {
		try {

			TepDuLieu tepDuLieu = repository.findByMaDinhDanh(maDinhDanh).get();
			if (tepDuLieu == null) {
				throw new FileNotFoundException("File not found with ID: " + maDinhDanh);
			}

			// Đường dẫn tệp
			Path filePath = Paths.get(tepDuLieu.getDuongDanURL()).toAbsolutePath().normalize();
			Resource resource = new FileSystemResource(filePath);

			if (!resource.exists()) {
				throw new FileNotFoundException("File not found at: " + filePath);
			}


			return ResponseEntity.ok()
					.body(resource);
		} catch (Exception e) {
			log.error("Error when downloading file: ", e);
			throw new RuntimeException("File download failed");
		}
	}
	@Override
	public TepDuLieu saveFileText(String contentInFile, boolean original) {
		try {
			String uploadDir = PortalUtil.remakeStaticResourceDir(
					GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_SAVE_DIR)));
			Path filePath = Paths.get(uploadDir).toAbsolutePath().normalize();
			File uploadDirectory = new File(filePath.toString());

			// Create directory if it doesn't exist
			if (!uploadDirectory.exists()) {
				uploadDirectory.mkdirs();
			}

			String maDinhDanh = UUID.randomUUID().toString();
			String type = ".edxml";
			if(original) {
				type = ".txt";
			}

			String urlSave = filePath + File.separator + maDinhDanh  + type ;

			Path path = Paths.get(urlSave);
			Files.write(path, contentInFile.getBytes());

			TepDuLieu tepDuLieu = new TepDuLieu();
			tepDuLieu.setMaDinhDanh(maDinhDanh);
			tepDuLieu.setTenTep(maDinhDanh + type);
			tepDuLieu.setDuongDanURL(urlSave);


			return repository.save(tepDuLieu);
		} catch (Exception e) {
			log.error("Error when upload fileTepDuLieu type String: ", e);
		}
		return null;
	}

	@Override
	public TepDuLieu saveFileUrl(String url, String edocId,String messagetype) {
		String maDinhDanh = UUID.randomUUID().toString();
		TepDuLieu tepDuLieu = new TepDuLieu();
		tepDuLieu.setMaDinhDanh(maDinhDanh);
		tepDuLieu.setTenTep(edocId + ".edxml");
		tepDuLieu.setDuongDanURL(url);
		tepDuLieu.setDinhDangGoiTin(messagetype);
		LoaiNguonLuuTruExt loaiNguonLuuTruExt = new LoaiNguonLuuTruExt();
		loaiNguonLuuTruExt.setMaMuc(Constant.GET_EDOC);
		tepDuLieu.setLoaiNguonLuuTru(loaiNguonLuuTruExt);
		tepDuLieu.setDinhDangTep("edxml");
        try {
            tepDuLieu.setKichThuocTep(this.getFileSizeFromLocal(url));
        } catch (IOException e) {
            tepDuLieu.setKichThuocTep(0L);
        }


        return repository.save(tepDuLieu);
	}
	private long getFileSizeFromLocal(String filePath) throws IOException {
		File file = new File(filePath);

		// Kiểm tra tệp có tồn tại không
		if (!file.exists()) {
			throw new IOException("Không tìm thấy tệp: " + filePath);
		}

		// Lấy kích thước tệp
		return file.length();
	}


	@Override
	public File viewFile(String maDinhDanh) {
		try {
			Optional<TepDuLieu> tepDuLieu = repository.findByMaDinhDanh(maDinhDanh);

			if(!tepDuLieu.isPresent()) {
				throw new Exception("Ko co trong DB");
			}

			String urlFile = tepDuLieu.get().getDuongDanURL();

			if(Validator.isNull(urlFile)) {
				throw new Exception("Ko co Duong dan url");
			}

			File file = new File(urlFile);
			if(!file.exists()) {
				throw new Exception("Ko load dc file tu duong danL: " + urlFile);
			}
			return file;
		} catch (Exception e) {
			log.error("File viewing maDinhDanh " + maDinhDanh + " is error ", e);
		}

		return null;
	}

	@Override
	public List<MonthlySummary> getSummaryByYearAndSourceType(int year, String loaiDuLieu) {
		return repository.getSummaryByYearAndSourceType(year, loaiDuLieu);
	}

	@Override
	public List<MonthlySummary> getSummaryByYearAndMultiSourceType(int year,  List<String> loaiDuLieu) {
		return repository.getSummaryByYearAndMultiSourceType(year, loaiDuLieu);
	}
	@Override
	public Page<TepDuLieu> filter(String keyword, Pageable pageable, String loaiNguonLuuTru_MaMuc) {
		Query query = new Query().with(pageable);

		List<Criteria> criteria = new ArrayList<>();

		if (Validator.isNotNull(keyword)) {

			List<Criteria> subCriterias = new ArrayList<>();
			Criteria c = Criteria.where("MaMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
			subCriterias.add(c);
			c = Criteria.where("TenMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
			subCriterias.add(c);

			criteria.add(new Criteria().orOperator(subCriterias));
		}
		if (Validator.isNotNull(loaiNguonLuuTru_MaMuc)) {
			Criteria c = Criteria.where("LoaiNguonLuuTru.MaMuc").is(loaiNguonLuuTru_MaMuc);
			criteria.add(c);
		}

		if (!criteria.isEmpty()) {
			query.addCriteria(new Criteria().andOperator(criteria));
		}

		return PageableExecutionUtils.getPage(mongoTemplate.find(query, TepDuLieu.class), pageable,
				() -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), TepDuLieu.class));
	}
	private String toLikeKeyword(String source) {
		return source.replaceAll("\\*", ".*");
	}

}
