package com.example.integration.hub.service.impl;

import com.example.integration.constant.CacheConstant;
import com.example.integration.hub.service.CacheService;
import com.example.integration.hub.service.GrpcClientService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fds.flex.context.model.User;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

@Service
public class CacheServiceImpl implements CacheService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private GrpcClientService grpcClientService;

    @Override
    //@Cacheable(cacheNames = {"count"}, key = "{#queryString, #classObj.getSimpleName()}")
    public Long countByQuery(String queryString, Query query, Class classObj) {
        return mongoTemplate.count(Query.of(query).limit(-1).skip(-1), classObj);
    }

    @Override
    @Cacheable(value = CacheConstant.USER_CACHE, key = "{#jti, #sub}", condition = "#result != null")
    public User getUser(String jti, String sub) throws JsonProcessingException {
        JSONObject resp = grpcClientService.getDanhTinhDienTuByMaSoId(sub);
        if (resp.isEmpty())
            return null;
        User user = new User();
        user.build(resp.toString());
        user.setJti(jti);
        return user;
    }
}
