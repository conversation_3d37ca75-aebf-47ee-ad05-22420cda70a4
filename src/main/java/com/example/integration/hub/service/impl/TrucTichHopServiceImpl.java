package com.example.integration.hub.service.impl;

import com.example.integration.constant.Constant;
import com.example.integration.entity.TrucTichHop;
import com.example.integration.hub.service.TrucTichHopService;
import com.example.integration.repository.TrucTichHopRepository;
import com.fds.flex.common.ultility.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class TrucTichHopServiceImpl implements TrucTichHopService {
	@Autowired
	private TrucTichHopRepository repository;

	@Autowired
	private MongoTemplate mongoTemplate;
	
	@Override
	public long countAll() {
		return repository.count();
	}

	@Override
	public Optional<TrucTichHop> findById(String id) {
		return repository.findById(id);
	}

	@Override
	public Optional<TrucTichHop> findByHeThongKetNoi_MaKetNoiAndMaMuc(String maKetNoi, String maTruc, String kieuLoaiGoiTin) {
		return repository.findByHeThongKetNoi_MaKetNoiAndMaMuc(maKetNoi, maTruc);
	}

	@Override
	public void delete(TrucTichHop object) {
		repository.delete(object);
	}

	@Override
	public TrucTichHop update(TrucTichHop object) {

		return repository.save(object);
	}

	@Override
	public Map<String, TrucTichHop> update(Map<String, TrucTichHop> map) {
		map.forEach((k, v) -> {
			{
				repository.save(v);
			}
		});
		return map;
	}

	@Override
	public Page<TrucTichHop> filter(String keyword,Long tuNgay,Long denNgay, Pageable pageable) {
		Query query = new Query().with(pageable);

		List<Criteria> criteria = new ArrayList<>();

		if (Validator.isNotNull(keyword)) {

			List<Criteria> subCriterias = new ArrayList<>();
			Criteria c = Criteria.where("MaMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
			subCriterias.add(c);
			c = Criteria.where("TenMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
			subCriterias.add(c);

			criteria.add(new Criteria().orOperator(subCriterias));
		}
		if (Validator.isNotNull(tuNgay)) {

			Criteria c = Criteria.where("ThoiGianTao").gte(tuNgay);
			criteria.add(c);
		}

		if (Validator.isNotNull(denNgay)) {
			Criteria c = Criteria.where("ThoiGianTao").lte(denNgay);
			criteria.add(c);
		}
		if (!criteria.isEmpty()) {
			query.addCriteria(new Criteria().andOperator(criteria));
		}

		return PageableExecutionUtils.getPage(mongoTemplate.find(query, TrucTichHop.class), pageable,
				() -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), TrucTichHop.class));
	}

	@Override
	public List<TrucTichHop> findAll() {
		return repository.findAll();
	}

	private String toLikeKeyword(String source) {
		return source.replaceAll("\\*", ".*");
	}
}
