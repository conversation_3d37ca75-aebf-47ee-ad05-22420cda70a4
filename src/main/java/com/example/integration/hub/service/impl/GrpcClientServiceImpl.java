package com.example.integration.hub.service.impl;

import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.hub.service.GrpcClientService;
import com.fds.flex.common.exception.InternalServerException;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.utility.string.StringPool;

import com.fds.flex.grpc.*;

import io.grpc.ManagedChannel;
import io.grpc.Metadata;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.stub.MetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;
import java.util.Map;

import static io.grpc.netty.shaded.io.grpc.netty.NegotiationType.TLS;

@Service
@Slf4j
public class GrpcClientServiceImpl implements GrpcClientService {

    public JSONObject fallback_getDanhTinhDienTuByMaSoId(String maSoId, Throwable throwable) throws InternalServerException {
        log.error("Error connect to Grpc : " + maSoId);
        return new JSONObject();
    }

    public JSONArray fallback_getAllTaiNguyenHeThong(Throwable throwable) throws InternalServerException {
        log.error("Error connect to Grpc : " + throwable.getMessage());
        return new JSONArray();
    }


    ManagedChannel createSecuredChannel() {

        Map<String, Object> properties = PropKey.getKeyMap();
        String host = "localhost";
        int port = 8080;

        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }

            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }
        }
        };
        try {
            return NettyChannelBuilder.forAddress(host, port).useTransportSecurity()
                    .sslContext(GrpcSslContexts.forClient().trustManager(trustAllCerts[0]).build()).negotiationType(TLS)
                    .build();
        } catch (Exception ex) {
            throw new RuntimeException();
        }
    }

    @Override
    public void updateTimeoutFlexAuth(String timeout) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        JSONObject principalObj = new JSONObject(authentication.getPrincipal());
        String token = principalObj.getString(Constant.TOKENVALUE);
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of(HttpHeaders.AUTHORIZATION, Metadata.ASCII_STRING_MARSHALLER),
                Constant.BEARER_TYPE + StringPool.SPACE + token);

        ManagedChannel channel = createSecuredChannel();
        try {
            SSOUpdateGrpc.SSOUpdateBlockingStub ssoUpdateBlockingStub = SSOUpdateGrpc.newBlockingStub(channel)
                    .withInterceptors(MetadataUtils.newAttachHeadersInterceptor(metadata));

            SSOUpdateOuterClass.TimeoutAccessTokenRequest request = SSOUpdateOuterClass.TimeoutAccessTokenRequest.newBuilder()
                    .setNewValue(timeout).build();

            ssoUpdateBlockingStub.updateTimeoutAccessToken(request);

            channel.shutdown();
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
    }

    @Override
    public void themTruyCapTaiNguyen(String data) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        JSONObject principalObj = new JSONObject(authentication.getPrincipal());
        String token = principalObj.getString(Constant.TOKENVALUE);
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of(HttpHeaders.AUTHORIZATION, Metadata.ASCII_STRING_MARSHALLER),
                Constant.BEARER_TYPE + StringPool.SPACE + token);

        ManagedChannel channel = createSecuredChannel();

        try {
            TruyCapTaiNguyenGrpc.TruyCapTaiNguyenBlockingStub truyCapTaiNguyenBlockingStub = TruyCapTaiNguyenGrpc.newBlockingStub(
                    channel).withInterceptors(MetadataUtils.newAttachHeadersInterceptor(metadata));

            TruyCapTaiNguyenOuterClass.TruyCapTaiNguyenRequest request = TruyCapTaiNguyenOuterClass.TruyCapTaiNguyenRequest.newBuilder()
                    .setContent(data).build();

            truyCapTaiNguyenBlockingStub.taoTruyCapTaiNguyen(request);

            channel.shutdown();
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
    }

    @Override
//    @HystrixCommand(groupKey = "GrpcClientServiceImpl", fallbackMethod = "fallback_getDanhTinhDienTuByMaSoId")
//    @Cacheable(value = "getDanhTinhDienTuByMaSoId", key = "#maSoId")
    public JSONObject getDanhTinhDienTuByMaSoId(String maSoId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        JSONObject principalObj = new JSONObject(authentication.getPrincipal());
        String token = principalObj.getString(Constant.TOKENVALUE);
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of(HttpHeaders.AUTHORIZATION, Metadata.ASCII_STRING_MARSHALLER),
                Constant.BEARER_TYPE + StringPool.SPACE + token);

        ManagedChannel channel = createSecuredChannel();


        DanhTinhDienTuGrpc.DanhTinhDienTuBlockingStub danhTinhDienTuBlockingStub = DanhTinhDienTuGrpc.newBlockingStub(
                channel).withInterceptors(MetadataUtils.newAttachHeadersInterceptor(metadata));

        DanhTinhDienTuOuterClass.FindByMaSoIdRequest request = DanhTinhDienTuOuterClass.FindByMaSoIdRequest.newBuilder()
                .setMaSoId(maSoId).build();

        DanhTinhDienTuOuterClass.APIResponse response = danhTinhDienTuBlockingStub.findByMaSoId(request);

        channel.shutdown();

        if (response.getStatus() == 200)
            return new JSONObject(response.getResp());
        return new JSONObject();
    }

}
