package com.example.integration.hub.service;

import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.entity.MonthlySummary;
import com.example.integration.entity.TepDuLieu;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Optional;

public interface TepDuLieuService {
	Optional<TepDuLieu> findByMaDinhDanh(String maDinhDanh);

	Optional<TepDuLieu> findById(String id);

	Optional<TepDuLieu> findByDuongDanURL(String duongDanURL);

	void deleteTepDuLieu(TepDuLieu tepDuLieu);

	TepDuLieu updateTepDuLieu(TepDuLieu tepDuLieu);

	TepDuLieu uploadFile(MultipartFile file, String loaiNguonLuuTru,String messagetype);
	ResponseEntity<Resource> downloadFile(String maDinhDanh);
	TepDuLieu saveFileText(String contentInFile,  boolean original);

	TepDuLieu saveFileUrl(String url, String edocId,String messagetype);

	File viewFile(String maDinhDanh);

	List<MonthlySummary> getSummaryByYearAndSourceType(int year, String loaiDuLieu);
	List<MonthlySummary> getSummaryByYearAndMultiSourceType(int year,  List<String> loaiDuLieu);
	Page<TepDuLieu> filter(String keyword, Pageable pageable, String loaiNguonLuuTru_MaMuc);

}
