package com.example.integration.hub.service.impl;


import com.example.integration.base.entity.TrangThaiDuLieu;
import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.ApiConstant;
import com.example.integration.constant.Constant;
import com.example.integration.dto.req.HangDoiGoiTinReqDTO;
import com.example.integration.entity.*;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.example.integration.entity.ext.TrangThaiLienThongExt;
import com.example.integration.hub.action.SDKVXPAction;
import com.example.integration.hub.action.SendNotifiAction;
import com.example.integration.hub.action.impl.SDKVXPActionImpl;
import com.example.integration.hub.service.DonViLienThongService;
import com.example.integration.hub.service.HangDoiGoiTinService;
import com.example.integration.hub.service.TepDuLieuService;
import com.example.integration.hub.service.TrucTichHopService;
import com.example.integration.hub.util.FunctionCallCounter;
import com.example.integration.hub.util.TokenUtil;
import com.example.integration.repository.DonViLienThongRepository;
import com.example.integration.repository.HangDoiGoiTinRepository;
import com.example.integration.repository.LogDonViLienThongRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.vpcp.services.AgencyServiceImp;
import com.vpcp.services.VnptProperties;
import com.vpcp.services.model.*;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Setter
@Getter
@Service
@ConfigurationProperties(prefix = "integration.hub.lgsp")
@Slf4j
public class SchedulerService {
    @Autowired
    RestTemplate restTemplate;

    @Autowired
    DonViLienThongService donViLienThongService;

    @Autowired
    LogDonViLienThongRepository repository;
    @Autowired
    DonViLienThongRepository donViLienThongrepository;
    @Autowired
    HangDoiGoiTinRepository hangDoiGoiTinRepository;
    @Autowired
    TrucTichHopService trucTichHopService;

    @Autowired
    HangDoiGoiTinService hangDoiGoiTinService;

    @Autowired
    TepDuLieuService tepDuLieuService;

    private String hostLgsp;
    private String secretVDXP;
    private String tokenUrl;
    private String getReceivedEdocListUrl;
    private String sendEdocUrl;
    private String getEdocUrl;
    private String updateStatusUrl;
    private String systemId;
    private String version;
    private String serviceId;
    private String lgspTo;
    private String basicUsername;
    private String basicPassword;
    private String username;
    private String password;
    @Autowired
    private HangDoiGoiTinServiceImpl hangDoiGoiTinServiceImpl;

    @Autowired
    private SendNotifiAction sendNotifiAction;

    @Autowired
    private FunctionCallCounter functionCallCounter;

    public int startSendEdoc() {
        // Increment the SEND function call count
        functionCallCounter.incrementCount(FunctionCallCounter.FUNCTION_SEND);
        secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        version = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_VERSION), StringPool.BLANK);
        serviceId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SERVICEID), StringPool.BLANK);
        lgspTo = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_LGSPTO), StringPool.BLANK);
        basicUsername = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_USERNAME), StringPool.BLANK);
        basicPassword = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_PASSWORD), StringPool.BLANK);
        username = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_USERNAME), StringPool.BLANK);
        password = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_PASSWORD), StringPool.BLANK);

        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);

        log.info("Version: " + version + ", systemId:" + systemId + ", lgspTo: " + lgspTo);

        int countDocSendSuccess = 0;

        try {
            List<TrucTichHop> trucTichHops = trucTichHopService.findAll();

            if (Validator.isNull(trucTichHops) || trucTichHops.isEmpty()) {
                log.warn("No tructichhop was found");
                return 0;
            }
            Pageable pageable = PageRequest.of(0, 10);
            Page<HangDoiGoiTin> pageHangDoiGoiTin = hangDoiGoiTinService.filterForScheduler(Constant.SEND_EDOC, Constant.STATUS_INITIAL, Constant.HETHONG_SERVER, pageable);

            if (Validator.isNull(pageHangDoiGoiTin)) {
                log.warn("No Page<HangDoiGoiTin> was found");
                return 0;
            }

            List<HangDoiGoiTin> hangDoiGoiTins = pageHangDoiGoiTin.getContent();

            if (Validator.isNull(hangDoiGoiTins) || hangDoiGoiTins.isEmpty()) {
                log.warn("No List<HangDoiGoiTin> was found");
                return 0;
            }

            for (HangDoiGoiTin hangDoiGoiTin : hangDoiGoiTins) {
                try {
                    TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_PROCESSING);

                    String statusSendOutside = Constant.STATUS_INITIAL;
                    for (HeThongKetNoiExt noiNhanGoiTin : hangDoiGoiTin.getNoiNhanGoiTin()) {
                        if (noiNhanGoiTin.getGiaoThucKetNoi().getMaMuc().equals(Constant.HETHONG_SERVER)
                                && noiNhanGoiTin.getTrangThaiLienThong().getMaMuc().equals(Constant.STATUS_INITIAL)) {
                            //chỉ gửi cho đơn vị ngoài
                            noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                            noiNhanGoiTin.setThoiGianKetNoi(System.currentTimeMillis());
                            hangDoiGoiTinService.update(hangDoiGoiTin);
                            ObjectMapper ob = new ObjectMapper();
                            //Chỉ gửi văn bản liên thông lên QG 1 lần
                            if (statusSendOutside.equals(Constant.STATUS_INITIAL)) {
                                SendEdocResult result = this.sendEdocUseSDK1(sdkVxpAction, hangDoiGoiTin);
                                boolean successSendEdoc = result != null && Constant.MESSAGE_OKE.equals(result.getStatus());
                                if (successSendEdoc) {
                                    trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_DONE);
                                    noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                    noiNhanGoiTin.setThoiGianKetNoi(System.currentTimeMillis());
                                    hangDoiGoiTin.setNoiDungTraVe(ob.writeValueAsString(result));
                                    hangDoiGoiTinService.update(hangDoiGoiTin);
                                    statusSendOutside = Constant.STATUS_DONE;
                                } else {
                                    trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_FAIL);
                                    noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                    noiNhanGoiTin.setThoiGianKetNoi(System.currentTimeMillis());
                                    hangDoiGoiTinService.update(hangDoiGoiTin);
                                    statusSendOutside = Constant.STATUS_FAIL;
                                    //Send Notify

                                    if (result == null || result.getStatus() == null || result.getErrorCode() == null || result.getDocID() == null) {
                                        String message = "Gửi edoc lỗi cho gói tin: " + hangDoiGoiTin.getMaGoiTin() + "\n From: " + hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi() + "\n To: "   + "\n Message VDXP: " + " {status: FAIL, ErrorCode: 1, ErrorDesc: null, DocId:}" ;
                                        hangDoiGoiTin.setNoiDungTraVe(ob.writeValueAsString(result));
                                        hangDoiGoiTinRepository.save(hangDoiGoiTin);
                                        HeThongKetNoiExt heThongGuiVanBan = trucTichHops.get(0).getHeThongKetNoi().get(0);


//                                        sendNotifiAction.sendNotificationTele(message, heThongGuiVanBan.getTelegram());
                                        sendNotifiAction.sendEmail(new InfoMail(heThongGuiVanBan.getEmail(), "Hệ thống gửi văn bản không thành công", message));
                                    }else {
                                        if(result.getStatus().equals(Constant.MESSAGE_OKE)) {
                                            String message = "Gửi edoc thành công cho gói tin: " + hangDoiGoiTin.getMaGoiTin() + "\n From: " + hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi() + "\n To: "   + "\n Message VDXP: " + ob.writeValueAsString(result) ;
                                            hangDoiGoiTin.setNoiDungTraVe(ob.writeValueAsString(result));
                                            hangDoiGoiTinRepository.save(hangDoiGoiTin);
                                            HeThongKetNoiExt heThongGuiVanBan = trucTichHops.get(0).getHeThongKetNoi().get(0);


//                                            sendNotifiAction.sendNotificationTele(message, heThongGuiVanBan.getTelegram());
                                            sendNotifiAction.sendEmail(new InfoMail(heThongGuiVanBan.getEmail(), "Hệ thống gửi văn bản không thành công", message));
                                        }
                                        else {
                                            String message = "Gửi edoc lỗi cho gói tin: " + hangDoiGoiTin.getMaGoiTin() + "\n From: " + hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi() + "\n To: "  + "\n Message VDXP: " + ob.writeValueAsString(result) ;
                                            hangDoiGoiTin.setNoiDungTraVe(ob.writeValueAsString(result));
                                            hangDoiGoiTinRepository.save(hangDoiGoiTin);
                                            HeThongKetNoiExt heThongGuiVanBan = trucTichHops.get(0).getHeThongKetNoi().get(0);


//                                            sendNotifiAction.sendNotificationTele(message, heThongGuiVanBan.getTelegram());
                                            sendNotifiAction.sendEmail(new InfoMail(heThongGuiVanBan.getEmail(), "Hệ thống gửi văn bản không thành công", message));
                                        }

                                    }

//                                    String message = "Gửi edoc lỗi cho gói tin: " + hangDoiGoiTin.getMaGoiTin() + "\n From: " + hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi() + "\n To: " + danhSachMaKetNoi  + "\n Message VDXP: " + ob.writeValueAsString(result) ;
//                                    HeThongKetNoiExt heThongGuiVanBan = trucTichHops.get(0).getHeThongKetNoi().get(0);
//                                    sendNotifiAction.sendNotificationTele(message, heThongGuiVanBan.getTelegram());
//                                    sendNotifiAction.sendEmail(new InfoMail(heThongGuiVanBan.getEmail(), "Hệ thống gửi văn bản không thành công", message));
                                }

                            } else if (statusSendOutside.equals(Constant.STATUS_FAIL)) {
                                trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_FAIL);
                                noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                hangDoiGoiTinService.update(hangDoiGoiTin);
                            } else {
                                trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_DONE);
                                noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                hangDoiGoiTinService.update(hangDoiGoiTin);
                            }
                        }
                    }

                    countDocSendSuccess++;
                    log.info("Goi with madinhdanh" + hangDoiGoiTin.getMaDinhDanh() + "has been send successful!");
                } catch (Exception e) {
                    log.warn("Hang doi goi tin: " + hangDoiGoiTin.getMaDinhDanh() + " send fail: ", e);
                }
            }
        } catch (Exception e) {
            log.error("Error when sendEdoc: ", e);
        }
        return countDocSendSuccess;
    }

    public void notifyTrucAction() {
        List<TrucTichHop> trucTichHops = trucTichHopService.findAll();

        if (Validator.isNull(trucTichHops) || trucTichHops.isEmpty()) {
            log.warn("No tructichhop was found");
            return;
        }
        HeThongKetNoiExt heThongBNG = trucTichHops.get(0).getHeThongKetNoi().get(0);

        String baseMessage = "Trong ngày trục đã request lên quốc gia 23 lần";

        // Build message for Telegram notification
        StringBuilder teleMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanHanhTrucTime is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanHanhTrucTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            teleMessage.append("\nThời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanHanhTrucUnit is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanHanhTrucUnit) {
            teleMessage.append("\nĐơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add notification type if isShowVanHanhTrucType is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanHanhTrucType) {
            teleMessage.append("\nLoại thông báo: Vận hành trục");
        }

        // Build message for Email notification
        StringBuilder emailMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanHanhTrucTime is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanHanhTrucTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            emailMessage.append("<br/>Thời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanHanhTrucUnit is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanHanhTrucUnit) {
            emailMessage.append("<br/>Đơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add notification type if isShowVanHanhTrucType is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanHanhTrucType) {
            emailMessage.append("<br/>Loại thông báo: Vận hành trục");
        }

//        sendNotifiAction.sendNotificationTele(teleMessage.toString(), heThongBNG.getTelegram());
        sendNotifiAction.sendEmail(new InfoMail(heThongBNG.getEmail(), "Thông báo tình hình vận hành trục trong ngày", emailMessage.toString()));
    }

    public void runNotifyGuiNhan1Hour() {
        long one1HourAgo = System.currentTimeMillis() - 3600000;
        List<HangDoiGoiTin> goiTinNhans = hangDoiGoiTinService.findByKieuLoaiGoiTinAndThoiGianGui(Constant.GET_EDOC, one1HourAgo);

        // Always set counts, even if lists are null or empty
        int countGoiTinNhan = Validator.isNotNull(goiTinNhans) && !goiTinNhans.isEmpty() ? goiTinNhans.size() : 0;
        List<HangDoiGoiTin> goiTinGuis = hangDoiGoiTinService.findByKieuLoaiGoiTinAndThoiGianGui(Constant.SEND_EDOC, one1HourAgo);
        int countGoiTinGui = Validator.isNotNull(goiTinGuis) && !goiTinGuis.isEmpty() ? goiTinGuis.size() : 0;

        String baseMessage = "Trong một giờ gần nhất đã nhận " + countGoiTinNhan + " và gửi " + countGoiTinGui + " văn bản";

        // Always proceed with notification, even if counts are 0
        List<TrucTichHop> trucTichHops = trucTichHopService.findAll();

        if (Validator.isNull(trucTichHops) || trucTichHops.isEmpty()) {
            log.warn("No tructichhop was found");
            return;
        }
        HeThongKetNoiExt heThongBNG = trucTichHops.get(0).getHeThongKetNoi().get(0);

        // Build message for Telegram notification
        StringBuilder teleMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanBanOneHourTime is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanBanOneHourTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            teleMessage.append("\nThời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanBanOneHourUnit is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanBanOneHourUnit) {
            teleMessage.append("\nĐơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add document type if isShowVanBanOneHourType is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanBanOneHourType) {
            teleMessage.append("\nLoại thông báo: Thống kê 1 giờ");
        }

        // Build message for Email notification
        StringBuilder emailMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanBanOneHourTime is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanBanOneHourTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            emailMessage.append("<br/>Thời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanBanOneHourUnit is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanBanOneHourUnit) {
            emailMessage.append("<br/>Đơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add document type if isShowVanBanOneHourType is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanBanOneHourType) {
            emailMessage.append("<br/>Loại thông báo: Thống kê 1 giờ");
        }

//        sendNotifiAction.sendNotificationTele(teleMessage.toString(), heThongBNG.getTelegram());
        sendNotifiAction.sendEmail(new InfoMail(heThongBNG.getEmail(), "Thông báo văn bản gửi nhận", emailMessage.toString()));
    }

    /**
     * Sends notifications for documents that have been in the send queue for at least one day
     * with status "initial"
     */
    public void runNotifySendOneDayLate() {
        // Calculate timestamp for one day ago
        long oneDayAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000);

        // Find documents in the queue that are older than one day with status INITIAL
        List<HangDoiGoiTin> lateDocuments = hangDoiGoiTinService.findByKieuLoaiGoiTinAndTrangThaiAndThoiGianGuiLessThan(
                Constant.SEND_EDOC, Constant.STATUS_INITIAL, oneDayAgo);

        // Initialize count to 0
        int count = 0;

        // Set count if documents exist
        if (Validator.isNotNull(lateDocuments) && !lateDocuments.isEmpty()) {
            count = lateDocuments.size();
        } else {
            log.info("No late documents found in the send queue");
            // Continue with notification with count = 0
        }

        String baseMessage = "Có " + count + " văn bản gửi đi đang bị trễ (trên 1 ngày) với trạng thái chưa xử lý";

        // Get notification recipients
        List<TrucTichHop> trucTichHops = trucTichHopService.findAll();
        if (Validator.isNull(trucTichHops) || trucTichHops.isEmpty()) {
            log.warn("No tructichhop was found");
            return;
        }

        HeThongKetNoiExt heThongBNG = trucTichHops.get(0).getHeThongKetNoi().get(0);

        // Build message for Telegram notification
        StringBuilder teleMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanBanLateTime is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanBanLateTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            teleMessage.append("\nThời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanBanLateUnit is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanBanLateUnit) {
            teleMessage.append("\nĐơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add document type if isShowVanBanLateType is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanBanLateType) {
            teleMessage.append("\nLoại văn bản: Gửi đi");
        }

        // Build message for Email notification
        StringBuilder emailMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanBanLateTime is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanBanLateTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            emailMessage.append("<br/>Thời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanBanLateUnit is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanBanLateUnit) {
            emailMessage.append("<br/>Đơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add document type if isShowVanBanLateType is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanBanLateType) {
            emailMessage.append("<br/>Loại văn bản: Gửi đi");
        }

        // Send notifications
//        sendNotifiAction.sendNotificationTele(teleMessage.toString(), heThongBNG.getTelegram());

        sendNotifiAction.sendEmail(new InfoMail(heThongBNG.getEmail(), "Thông báo văn bản gửi trễ", emailMessage.toString()));

        log.info("Sent notification for " + count + " late documents");
    }

    /**
     * Sends notifications about the total count of function calls (GET, SEND, UPDATE)
     * and resets the counts after sending
     */
    public void sendFunctionCallCountNotification() {
        // Load counts from file in case they were updated by another instance
        functionCallCounter.loadCountsFromFile();

        // Get the counts
        int getCount = functionCallCounter.getCount(FunctionCallCounter.FUNCTION_GET);
        int sendCount = functionCallCounter.getCount(FunctionCallCounter.FUNCTION_SEND);
        int updateCount = functionCallCounter.getCount(FunctionCallCounter.FUNCTION_UPDATE);

        // Create the base notification message
        String baseMessage = "Tổng số lần gọi hàm trong ngày:\n" +
                "- GET: " + getCount + "\n" +
                "- SEND: " + sendCount + "\n" +
                "- UPDATE: " + updateCount;

        // Get notification recipients
        List<TrucTichHop> trucTichHops = trucTichHopService.findAll();
        if (Validator.isNull(trucTichHops) || trucTichHops.isEmpty()) {
            log.warn("No tructichhop was found");
            return;
        }

        HeThongKetNoiExt heThongBNG = trucTichHops.get(0).getHeThongKetNoi().get(0);

        // Build message for Telegram notification
        StringBuilder teleMessage = new StringBuilder(baseMessage);

        // Add time if isShowVanHanhTrucTime is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanHanhTrucTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            teleMessage.append("\nThời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanHanhTrucUnit is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanHanhTrucUnit) {
            teleMessage.append("\nĐơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add notification type if isShowVanHanhTrucType is true
        if (heThongBNG.getTeleNotification() != null && heThongBNG.getTeleNotification().isShowVanHanhTrucType) {
            teleMessage.append("\nLoại thông báo: Thống kê gọi trục");
        }

        // Build message for Email notification
        // For email, we need to replace \n with <br/>
        String emailBaseMessage = baseMessage.replace("\n", "<br/>");
        StringBuilder emailMessage = new StringBuilder(emailBaseMessage);

        // Add time if isShowVanHanhTrucTime is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanHanhTrucTime) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
            String currentTime = dateFormat.format(new Date());
            emailMessage.append("<br/>Thời điểm gửi thông báo: ").append(currentTime);
        }

        // Add unit if isShowVanHanhTrucUnit is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanHanhTrucUnit) {
            emailMessage.append("<br/>Đơn vị: ").append(heThongBNG.getTenKetNoi());
        }

        // Add notification type if isShowVanHanhTrucType is true
        if (heThongBNG.getEmailNotification() != null && heThongBNG.getEmailNotification().isShowVanHanhTrucType) {
            emailMessage.append("<br/>Loại thông báo: Thống kê gọi hàm");
        }

        // Send notifications
//        sendNotifiAction.sendNotificationTele(teleMessage.toString(), heThongBNG.getTelegram());
        sendNotifiAction.sendEmail(new InfoMail(heThongBNG.getEmail(), "Thống kê gọi hàm trong ngày", emailMessage.toString()));

        // Reset the counts after sending the notification
        functionCallCounter.resetCounts();

        log.info("Sent function call count notification and reset counts");
    }

    public int startReceiveEdoc() {
        // Increment the GET function call count
        functionCallCounter.incrementCount(FunctionCallCounter.FUNCTION_GET);
        secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        version = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_VERSION), StringPool.BLANK);
        serviceId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SERVICEID), StringPool.BLANK);
        lgspTo = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_LGSPTO), StringPool.BLANK);
        basicUsername = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_USERNAME), StringPool.BLANK);
        basicPassword = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_PASSWORD), StringPool.BLANK);
        username = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_USERNAME), StringPool.BLANK);
        password = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_PASSWORD), StringPool.BLANK);
        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);

        int countDocReceiveSuccess = 0;

        try {
            List<TrucTichHop> trucTichHops = trucTichHopService.findAll();

            if (Validator.isNull(trucTichHops) || trucTichHops.isEmpty()) {
                log.warn("No tructichhop was found");
                return 0;
            }

            for (TrucTichHop trucTichHop : trucTichHops) {
                JSONArray listEdocReceived = this.getListEdocUseSDK(sdkVxpAction);
                for (int i = 0; i < listEdocReceived.length(); i++) {
                    try {
                        JSONObject oneDoc = listEdocReceived.getJSONObject(i);
                        if (!oneDoc.has("docId") || Validator.isNull(oneDoc.getString("docId"))) {
                            log.warn("docId is null");
                            continue;
                        }

                        String edocId = oneDoc.getString("docId");

                        String duongDanFile = this.getEdocUseSDK(sdkVxpAction, edocId);

                        if (Validator.isNull(duongDanFile)) {
                            continue;
                        }

                        oneDoc.put("urlFile", duongDanFile);
                        JsonNode root = PortalUtil.parseTxtFileUsingStream(duongDanFile);
//                        JsonNode root = null;

                        HangDoiGoiTin hangDoiGoiTin = this.saveEdocVer2(oneDoc, trucTichHop, PortalUtil.getEdxmlContent(oneDoc.getString("messagetype"), root));

                        if (Validator.isNull(hangDoiGoiTin)) {
                            continue;
                        }

                        log.info("Temp EDOCId: " + edocId + " saved");

                        boolean resultUpdateStatus = this.updateStatusUseSDK(sdkVxpAction, edocId, Constant.DONE);

                        if (resultUpdateStatus) {
                            log.info("Document EDOC " + edocId + " has been verify successful!");
                            countDocReceiveSuccess++;
                            continue;
                        }

                        log.warn("Document EDOC " + edocId + " has been verify fail");
                        hangDoiGoiTinService.delete(hangDoiGoiTin);
                        log.warn("Document EDOC " + edocId + " has been DELETE");
                    } catch (Exception e) {
                        log.warn("Error when running one Edoc " + e.getMessage());
                    }
                }

            }

            return countDocReceiveSuccess;
        } catch (Exception e) {
            log.error("Error when receiveEdoc: ", e);
        }
        return countDocReceiveSuccess;
    }

    public String getToken() {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            headers.set("X-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-authorization",
                    "Basic ".concat(Base64.getEncoder().encodeToString((basicUsername + ":" + basicPassword).getBytes())));

            String jsonBody = String.format(
                    "username=%s&password=%s&grant_type=%s",
                    username,
                    password,
                    "password"
            );
            log.info("body: " + jsonBody);
            log.info("header auth: " + headers.get("X-lgsp-authorization"));
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
            log.info("Calling API LGSP: " + hostLgsp + tokenUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(hostLgsp + tokenUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request getToken was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());
                return responseJSON.getString(ApiConstant.ACCESS_TOKEN);
            }

            log.warn("Request getToken failed with status: " + response.getStatusCode());

        } catch (Exception e) {
            log.error("Error when getting token: ", e);
        }
        return null;
    }

    public void sendNotificationForVanBan(String status) {
        log.info("Sending notification for van ban...");

        if (status == null) {
            log.warn("Status parameter is null, returning without sending notification");
            return;
        }


        //Gui thong bao ho so ton
        List<HangDoiGoiTin> hangDoiGoiTinUnSendNoti = hangDoiGoiTinService.findByKieuLoaiGoiTinAndMaPhienBanAndTrangThai(
                Constant.SEND_EDOC, Constant.CHUA_GUI_THONG_BAO, status);

        // Initialize count to 0
        int count = 0;

        // Update documents if they exist
        if (Validator.isNotNull(hangDoiGoiTinUnSendNoti) && !hangDoiGoiTinUnSendNoti.isEmpty()) {
            count = hangDoiGoiTinUnSendNoti.size();
            for (HangDoiGoiTin oneHangDoi : hangDoiGoiTinUnSendNoti) {
                oneHangDoi.setMaPhienBan(Constant.DA_GUI_THONG_BAO);
                hangDoiGoiTinService.update(oneHangDoi);
            }
        }

        // Always send notification, even if count is 0
        List<TrucTichHop> trucTichHops = trucTichHopService.findAll();
        if (Validator.isNotNull(trucTichHops) && !trucTichHops.isEmpty()) {
            HeThongKetNoiExt heThongBNG = trucTichHops.get(0).getHeThongKetNoi().get(0);

            // Determine which notification settings to use based on status
            boolean isTeleShowTime = false;
            boolean isTeleShowUnit = false;
            boolean isEmailShowTime = false;
            boolean isEmailShowUnit = false;
            String messagePrefix;
            String emailSubject;

            if (Constant.STATUS_INITIAL.equals(status)) {
                // Use VanBanTon settings for INITIAL status
                messagePrefix = "Số lượng văn bản tồn: ";
                emailSubject = "Thông báo văn bản tồn";

                if (heThongBNG.getTeleNotification() != null) {
                    isTeleShowTime = heThongBNG.getTeleNotification().isShowVanBanTonTime;
                    isTeleShowUnit = heThongBNG.getTeleNotification().isShowVanBanTonUnit;
                }

                if (heThongBNG.getEmailNotification() != null) {
                    isEmailShowTime = heThongBNG.getEmailNotification().isShowVanBanTonTime;
                    isEmailShowUnit = heThongBNG.getEmailNotification().isShowVanBanTonUnit;
                }
            } else if (Constant.STATUS_FAIL.equals(status)) {

                // Use VanBanLoi settings for FAIL status
                messagePrefix = "Số lượng văn bản lỗi: ";
                emailSubject = "Thông báo văn bản lỗi";

                if (heThongBNG.getTeleNotification() != null) {
                    isTeleShowTime = heThongBNG.getTeleNotification().isShowVanBanLoiTime;
                    isTeleShowUnit = heThongBNG.getTeleNotification().isShowVanBanLoiUnit;
                }

                if (heThongBNG.getEmailNotification() != null) {
                    isEmailShowTime = heThongBNG.getEmailNotification().isShowVanBanLoiTime;
                    isEmailShowUnit = heThongBNG.getEmailNotification().isShowVanBanLoiUnit;
                }
            } else {

                // Default for other statuses
                messagePrefix = "Số lượng văn bản (" + status + "): ";
                emailSubject = "Thông báo văn bản";
            }

            // Build message for Telegram notification
            StringBuilder teleMessage = new StringBuilder(messagePrefix + count);

            // Add time if show time setting is true
            if (isTeleShowTime) {

                SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
                String currentTime = dateFormat.format(new Date());
                teleMessage.append("\nThời điểm gửi thông báo: ").append(currentTime);
            }

            // Add unit if show unit setting is true
            if (isTeleShowUnit) {

                teleMessage.append("\nĐơn vị: ").append(heThongBNG.getTenKetNoi());
            }

            // Build message for Email notification
            StringBuilder emailMessage = new StringBuilder(messagePrefix + count);

            // Add time if show time setting is true
            if (isEmailShowTime) {

                SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm");
                String currentTime = dateFormat.format(new Date());
                emailMessage.append("<br/>Thời điểm gửi thông báo: ").append(currentTime);
            }

            // Add unit if show unit setting is true
            if (isEmailShowUnit) {

                emailMessage.append("<br/>Đơn vị: ").append(heThongBNG.getTenKetNoi());
            }

//            sendNotifiAction.sendNotificationTele(teleMessage.toString(), heThongBNG.getTelegram());
            sendNotifiAction.sendEmail(new InfoMail(heThongBNG.getEmail(), emailSubject, emailMessage.toString()));
        } else {

            log.warn("No tructichhop was found for sending notification");
        }

    }

    private boolean sendEdocUseSDK(SDKVXPAction action, HangDoiGoiTin hangDoiGoiTin) throws JsonProcessingException {
        String urlLocalFile = hangDoiGoiTin.getNoiDungGoiTin().getDuongDanURL();
        String from = systemId;
        Map<String, String> headers = hangDoiGoiTin.getHeaders();
        String serviceType = "eDoc";
        String messageType = headers.get(Constant.HEADER_MESSAGE_TYPE);
        SendEdocResult result = action.sendEdoc(from, serviceType, messageType, urlLocalFile);
        if (Validator.isNull(result)) {
            log.warn("Request sendEdoc is null");
            return false;
        }

        if (Constant.MESSAGE_OKE.equalsIgnoreCase(result.getStatus())) {
            log.warn("Request sendEdoc is oke for unit " + from + ", docId: " + result.getDocID());
            return true;
        }

        log.warn("Request sendEdoc is error with status: " + result.getStatus() + ", errorDesc: " + result.getErrorDesc()
                + ", errorCode: " + result.getErrorCode() + ", docId: " + result.getDocID());
        return false;
    }

    private SendEdocResult sendEdocUseSDK1(SDKVXPAction action, HangDoiGoiTin hangDoiGoiTin) throws JsonProcessingException {
        String urlLocalFile = hangDoiGoiTin.getNoiDungGoiTin().getDuongDanURL();
        String from = systemId;
        Map<String, String> headers = hangDoiGoiTin.getHeaders();
        String serviceType = "eDoc";
        String messageType = headers.get(Constant.HEADER_MESSAGE_TYPE);

        SendEdocResult result = action.sendEdoc(from, serviceType, messageType, urlLocalFile);

        if (Validator.isNull(result)) {
            log.warn("Request sendEdoc is null");
            return null;
        }

        if (Constant.MESSAGE_OKE.equalsIgnoreCase(result.getStatus())) {
            log.warn("Request sendEdoc is oke for unit " + from + ", docId: " + result.getDocID());
        } else {
            log.warn("Request sendEdoc is error with status: " + result.getStatus()
                    + ", errorDesc: " + result.getErrorDesc()
                    + ", errorCode: " + result.getErrorCode()
                    + ", docId: " + result.getDocID());
        }

        return result;
    }

    private String getEdocUseSDK(SDKVXPAction action, String edocId) {
        GetEdocResult getEdocResult = action.getEdoc(edocId);
        if (Validator.isNull(getEdocResult) || Validator.isNull(getEdocResult.getFilePath())) {
            log.warn("File not found when call SDK for edocId: " + edocId);
            return null;
        }
        return getEdocResult.getFilePath();
    }

    private JSONArray getListEdocUseSDK(SDKVXPAction action) {
        JSONArray result = new JSONArray();
        GetReceivedEdocResult edocsResult = action.getListEdoc("eDoc", Constant.MESSAGE_TYPE_EDOC);

        GetReceivedEdocResult statusResult = action.getListEdoc("eDoc", Constant.MESSAGE_TYPE_STATUS);

        if (Validator.isNull(edocsResult) && Validator.isNull(statusResult)) {
            return result;
        }

        log.info("=====GetListEdocUseSDK: " + edocsResult.getKnobsticks().size());
        log.info("=====GetListStatusUseSDK: " + statusResult.getKnobsticks().size());

        JSONObject oneResult;
        for (Knobstick item : edocsResult.getKnobsticks()) {
            oneResult = new JSONObject();
            oneResult.put("docId", item.getId());
            oneResult.put("serviceType", item.getServiceType());
            oneResult.put("created_time", item.getCreatedTime());
            oneResult.put("updated_time", item.getUpdatedTime());
            oneResult.put("messagetype", item.getMessageType());
            oneResult.put("status", item.getStatus());
            oneResult.put("from", item.getFrom());
            oneResult.put("to", item.getTo());

            result.put(oneResult);
        }

        for (Knobstick item : statusResult.getKnobsticks()) {
            oneResult = new JSONObject();
            oneResult.put("docId", item.getId());
            oneResult.put("serviceType", item.getServiceType());
            oneResult.put("created_time", item.getCreatedTime());
            oneResult.put("updated_time", item.getUpdatedTime());
            oneResult.put("messagetype", item.getMessageType());
            oneResult.put("status", item.getStatus());
            oneResult.put("from", item.getFrom());
            oneResult.put("to", item.getTo());

            result.put(oneResult);
        }
        return result;
    }


    private boolean updateStatusUseSDK(SDKVXPAction action, String edocId, String status) {
        // Increment the UPDATE function call count
        functionCallCounter.incrementCount(FunctionCallCounter.FUNCTION_UPDATE);
        try {
            GetChangeStatusResult getChangeStatusResult = action.updateStatus(status, edocId);

            if (Validator.isNull(getChangeStatusResult)) {
                log.warn("getChangeStatusResult is null for edocId: " + edocId + ", status: " + status);
                return false;
            }

            if (getChangeStatusResult.getStatus().equalsIgnoreCase(Constant.MESSAGE_OKE)) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }


    private boolean sendEdoc(String token, HangDoiGoiTin hangDoiGoiTin, String maHeThongXuly) {

        try {

            String duongDanTep = hangDoiGoiTin.getNoiDungGoiTin().getDuongDanURL();
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_XML_VALUE);
            headers.set("MessageType", "edoc");
            headers.set("ServiceType", "eDoc");
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            hangDoiGoiTin.getHeaders().forEach((key, value) -> {
                headers.set(key, value);
            });
            log.info("Duong dan tep: " + duongDanTep);
            File file = new File(duongDanTep);

            if (Validator.isNull(file)) {
                log.warn("File not found with url: " + duongDanTep);
                return false;
            }

            String fileContent = new String(Files.readAllBytes(file.toPath()));
            log.info("FileContent: " + fileContent);
            HttpEntity<String> entity = new HttpEntity<>(fileContent, headers);
            log.info("Calling API LGSP: " + hostLgsp + sendEdocUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(hostLgsp + sendEdocUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request sendEdoc was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());

                if (!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request sendEdoc error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return false;
                }

                if (responseJSON.has("data") && Validator.isNotNull(responseJSON.getJSONArray("data"))) {
                    JSONObject oneData = responseJSON.getJSONArray("data").getJSONObject(0);
                    if (oneData.has("docId") && Validator.isNotNull(oneData.getString("docId"))) {
                        return true;
                    }
                }

                log.warn("Request sendEdoc has no docId or docId is empty");
                return false;
            }
            log.warn("Request sendEdoc failed with http status: " + response.getStatusCode());

        } catch (Exception e) {
            log.error("Error when sendEdoc: ", e);
        }
        return false;
    }

    private JSONArray getGetReceivedEdocList(String token, String clientReceiveEdoc) {
        JSONArray result = new JSONArray();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            headers.set("MessageType", "edoc");
            headers.set("X-lgsp-from", "notImportant");
            // X-lgsp-to là don vi nhan goi tin
            headers.set("X-lgsp-to", clientReceiveEdoc);
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            log.info("Calling API LGSP: " + hostLgsp + getReceivedEdocListUrl);
            ResponseEntity<String> response = restTemplate.exchange(hostLgsp + getReceivedEdocListUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request getGetReceivedEdocList was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());

                if (!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request getGetReceivedEdocList error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return result;
                }

                if (responseJSON.has("data") && Validator.isNotNull(responseJSON.getJSONObject("data"))
                        && responseJSON.getJSONObject("data").has("listResponse")) {
                    JSONArray data = responseJSON.getJSONObject("data").getJSONArray("listResponse");
                    if (data.length() > 0) {
                        return data;
                    }
                }

                log.warn("Request getGetReceivedEdocList has no DATA or DATA is empty");
                return result;
            }
            log.warn("Request getGetReceivedEdocList failed with http status: " + response.getStatusCode());
        } catch (Exception e) {
            log.error("Error when getGetReceivedEdocList: ", e);
        }
        return result;
    }

    private String getEdoc(String edocId, String clientReceiveEdoc, String token) {
        String result = "";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            headers.set("DocId", edocId);
            headers.set("X-lgsp-from", "notImportant");
            headers.set("X-lgsp-to", "notImportant");
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            log.info("Calling API LGSP: " + hostLgsp + getEdocUrl);
            ResponseEntity<String> response = restTemplate.exchange(hostLgsp + getEdocUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request getEdoc" + edocId + " was successful ");

                JSONObject responseJSON = new JSONObject(response.getBody());

                if (!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request getEdoc" + edocId + " error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return result;
                }

                if (responseJSON.has("data")) {
                    return responseJSON.getString("data");
                }

                log.warn("Request getGetReceivedEdocList has no DATA or DATA is empty");
                return result;

            }
            log.warn("Request getEdoc" + edocId + " failed with http status: " + response.getStatusCode());

        } catch (Exception e) {
            log.error("Error when getEdoc " + edocId + ": ", e);
        }
        return result;
    }

    private boolean updateStatus(String edocId, String status, String clientReceiveEdoc, String token) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            headers.set("DocId", edocId);
            headers.set("Status", status);
            headers.set("X-lgsp-from", clientReceiveEdoc);
            headers.set("X-lgsp-to", lgspTo);
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            log.info("Calling API LGSP: " + hostLgsp + updateStatusUrl);
            ResponseEntity<String> response = restTemplate.exchange(hostLgsp + updateStatusUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request updateStatus" + edocId + " was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());

                if (!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request updateStatus" + edocId + " error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return false;
                }

                if (responseJSON.has("code") && responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    return true;
                }

                log.warn("Request updateStatus has no code or code is empty");
                return false;
            }
            log.warn("Request code" + edocId + " failed with http status: " + response.getStatusCode());
        } catch (Exception e) {
            log.error("Error when updateStatus " + edocId + ": ", e);
        }
        return false;
    }

    private HangDoiGoiTin saveEdocVer2(JSONObject oneEdocJson, TrucTichHop trucTichHop,HangDoiGoiTin.ContentEdoc contentEdoc) {
        try {
            String edocId = oneEdocJson.get("docId").toString();
            String timeVdxpCreate = oneEdocJson.get("created_time").toString();
            String timeVdxpUpdate = oneEdocJson.get("updated_time").toString();
            String serviceType = oneEdocJson.get("serviceType").toString();
            String messagetype = oneEdocJson.has("messagetype") ? oneEdocJson.get("messagetype").toString() : Constant.MESSAGE_TYPE_EDOC;
            String status = oneEdocJson.get("status").toString();
            String maNoiGui = oneEdocJson.get("from").toString();
            String maNoiNhan = oneEdocJson.get("to").toString();
            String urlFileSaved = oneEdocJson.get("urlFile").toString();

            // Check if maGoiTin already exists in HangDoiGoiTin, if so generate new UUID
            String maGoiTin = edocId;
            Optional<HangDoiGoiTin> existingHangDoiGoiTin = hangDoiGoiTinService.findByMaGoiTin(maGoiTin);
            if (existingHangDoiGoiTin.isPresent()) {
                maGoiTin = UUID.randomUUID().toString();
                log.info("MaGoiTin {} already exists, generated new UUID: {}", edocId, maGoiTin);
            }

            String maDinhDanh = UUID.randomUUID().toString();

            HangDoiGoiTin hangDoiGoiTin = new HangDoiGoiTin(false);
            List<HeThongKetNoiExt> heThongKetNoiExts = trucTichHop.getHeThongKetNoi();
            TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt();

            HeThongKetNoiExt heThongGui = new HeThongKetNoiExt();
            heThongGui.setMaKetNoi(maNoiGui);
            heThongGui.setThoiGianGui(System.currentTimeMillis());
            heThongGui.setTimeVdxpCreate(timeVdxpCreate);
            heThongGui.setTimeVdxpUpdate(timeVdxpUpdate);
            hangDoiGoiTin.setNoiGuiGoiTin(heThongGui);

//            for(HeThongKetNoiExt heThongKetNoiExt: heThongKetNoiExts) {
            HeThongKetNoiExt heThongKetNoiExt = new HeThongKetNoiExt();
            heThongKetNoiExt.setMaKetNoi(systemId);
            heThongKetNoiExt.setTenKetNoi("Trục văn bản");
            heThongKetNoiExt.setTimeVdxpCreate(timeVdxpCreate);
            heThongKetNoiExt.setTimeVdxpUpdate(timeVdxpUpdate);
            hangDoiGoiTin.getNoiNhanGoiTin().add(heThongKetNoiExt);
//            }

//            if (hangDoiGoiTin.getNoiNhanGoiTin().isEmpty()) {
//                log.warn("Save hangdoigoitin has been halt because of maNoiNhan hasn't register on TrucVanBan: " + maNoiNhan);
//                return null;
//            }

            TepDuLieu tepDuLieu = tepDuLieuService.saveFileUrl(urlFileSaved, maGoiTin,messagetype);
            hangDoiGoiTin.getNoiDungGoiTin().setMaDinhDanh(tepDuLieu.getMaDinhDanh());
            hangDoiGoiTin.setMaDinhDanh(maDinhDanh);
            hangDoiGoiTin.setMaGoiTin(maGoiTin);
            hangDoiGoiTin.setContentEdoc(contentEdoc);
            hangDoiGoiTin.getTrucTichHop().setMaMuc(trucTichHop.getMaMuc());
            hangDoiGoiTin.setKieuLoaiGoiTin(Constant.GET_EDOC);
            hangDoiGoiTin.setDinhDangGoiTin(messagetype.toLowerCase());
            return hangDoiGoiTinService.update(hangDoiGoiTin);

        } catch (Exception e) {
            log.error("Error when saveEdocToHangDoi " + oneEdocJson.get("docId") + ": ", e);
        }
        return null;
    }

//    private HangDoiGoiTin saveEdocToHangDoi(String edocId, String edocResult, String kieuLoaiGoiTin,
//                                            String maNoiGui, String maNoiNhan, TrucTichHop trucTichHop) {
//        try {
//            HangDoiGoiTin hangDoiGoiTin = new HangDoiGoiTin(false);
//            String maDinhDanh = UUID.randomUUID().toString();
//            String maGoiTin = edocId;
//            List<HeThongKetNoiExt> heThongKetNoiExts = trucTichHop.getHeThongKetNoi();
//            TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt();
//            for (HeThongKetNoiExt heThongKetNoiExt : heThongKetNoiExts) {
//                if (maNoiGui.equals(heThongKetNoiExt.getMaKetNoi())) {
//                    heThongKetNoiExt.setThoiGianGui(System.currentTimeMillis());
//                    hangDoiGoiTin.setNoiGuiGoiTin(heThongKetNoiExt);
//                }
//
//                if (maNoiNhan.equals(heThongKetNoiExt.getMaKetNoi())) {
//                    heThongKetNoiExt.setTrangThaiLienThong(trangThaiLienThongExt);
//                    hangDoiGoiTin.getNoiNhanGoiTin().add(heThongKetNoiExt);
//                }
//            }
//
//            TepDuLieu tepDuLieuGoc = tepDuLieuService.saveFileText(edocResult, true);
//
//            String edocDecoded = new String(Base64.getDecoder().decode(edocResult));
//
//            TepDuLieu tepDuLieuDecoded = tepDuLieuService.saveFileText(edocDecoded, false);
//
//            if (Validator.isNull(tepDuLieuDecoded) || Validator.isNull(tepDuLieuGoc)) {
//                log.warn("Tep du lieu is null when trying to saveEdocToHangDoi");
//                return null;
//            }
//
//            hangDoiGoiTin.getNoiDungGoiTin().setMaDinhDanh(tepDuLieuDecoded.getMaDinhDanh());
//            hangDoiGoiTin.getNoiDungGoiTin().setFileOriginal(tepDuLieuGoc.getMaDinhDanh());
//            hangDoiGoiTin.setMaDinhDanh(maDinhDanh);
//            hangDoiGoiTin.setMaGoiTin(maGoiTin);
//            hangDoiGoiTin.getTrucTichHop().setMaMuc(trucTichHop.getMaMuc());
//            hangDoiGoiTin.setKieuLoaiGoiTin(kieuLoaiGoiTin);
//            return hangDoiGoiTinService.update(hangDoiGoiTin);
//        } catch (Exception e) {
//            log.error("Error when saveEdocToHangDoi " + 0 + ": ", e);
//        }
//        return null;
//    }


    public ResponseEntity<?> getListAgencies(SDKVXPAction action) {
        String tenGoi = "SYSTEM ADMIN";
        try {
            String endpoint = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP),
                    StringPool.BLANK);
            String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID),
                    StringPool.BLANK);
            String secret = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP),
                    StringPool.BLANK);

            if(Validator.isBlank(endpoint) || Validator.isBlank(systemId) || Validator.isBlank(secret)) {
                return ResponseEntity.ok("Không đơn vị nào được đồng bộ!!");
            }

            GetAgenciesResult result = action.getListAgencies("", "","");

            List<Agency> agencies = result.getAgencies();
            if (agencies == null || agencies.isEmpty()) {
                return ResponseEntity.ok("Không có đơn vị nào được đồng bộ.");
            }


            int totalCount = 0, countCreated = 0, countUpdated = 0, countUnchanged = 0;
            StringBuilder logDetail = new StringBuilder();

            List<C_DonViLienThong> listToInsert = new ArrayList<>();
            List<String> maMucList = agencies.stream()
                    .map(Agency::getCode)
                    .collect(Collectors.toList());
            Map<String, C_DonViLienThong> existingMap = donViLienThongService
                    .findByMaMucIn(maMucList, TrangThaiDuLieu.TrangThai.ChinhThuc.getMaMuc())
                    .stream()
                    .collect(Collectors.toMap(
                            C_DonViLienThong::getMaMuc,
                            Function.identity(),
                            (existing, duplicate) -> existing
                    ));
            List<C_DonViLienThong> allEntitiesInDb = donViLienThongService.findAll();

            List<C_DonViLienThong> recordsToDelete = allEntitiesInDb.stream()
                    .filter(entity -> !maMucList.contains(entity.getMaMuc()))
                    .collect(Collectors.toList());
            Set<String> maMucFromSDK = new HashSet<>(maMucList);
            List<C_DonViLienThong> entitiesToUpdate = new ArrayList<>();
            for (Agency agency : agencies) {
                String maMuc = agency.getCode();
                String tenMuc = agency.getName();
                String email = agency.getMail();
                String pid  = agency.getPid();
                String id = agency.getId();

                if (pid == null || pid.trim().equals("0")) {
                    continue;
                }
                List<String> allIds = agencies.stream()
                        .filter(a -> a.getPid() != null && !a.getPid().trim().equals("0"))
                        .map(Agency::getId)
                        .collect(Collectors.toList());
                C_DonViLienThong existing = existingMap.get(maMuc);
                boolean isRoot = !allIds.contains(pid);

                if (existing != null) {
                    boolean changed = false;

                    if (!Objects.equals(tenMuc, existing.getTenMuc())) {
                        existing.setTenMuc(tenMuc);
                        changed = true;
                    }

                    if (!Objects.equals(email, existing.getMail())) {
                        existing.setMail(email);
                        changed = true;
                    }

                    if (!Objects.equals(id, existing.getID_DVLT())) {
                        existing.setID_DVLT(id);
                        changed = true;
                    }

                    if (!Objects.equals(pid, existing.getPid())) {
                        existing.setPid(pid);
                        changed = true;
                    }

                    if (isRoot && !"1".equals(existing.getMa())) {
                        existing.setMa("1");
                        changed = true;
                    }

                    if (changed && !entitiesToUpdate.contains(existing)) {
                        entitiesToUpdate.add(existing);
                        logDetail.append(String.format("Cập nhật: %s - %s\n", maMuc, tenMuc));
                    }
                } else {
                    C_DonViLienThong newDonVi = new C_DonViLienThong();
                    newDonVi.setMaMuc(maMuc);
                    newDonVi.setTenMuc(tenMuc);
                    newDonVi.setPid(pid);
                    newDonVi.setMail(email);
                    newDonVi.setID_DVLT((id));
                    if (isRoot) {
                        newDonVi.setMa("1");
                    }
                    listToInsert.add(newDonVi);
                    logDetail.append(String.format("Thêm mới: %s - %s\n", maMuc, tenMuc));


                }
                if (!recordsToDelete.isEmpty()) {
                    donViLienThongrepository.deleteAll(recordsToDelete);
                    for (C_DonViLienThong deleted : recordsToDelete) {
                        logDetail.append(String.format("Xóa: %s - %s\n", deleted.getMaMuc(), deleted.getTenMuc()));
                    }
                }

                totalCount++;
            }

            donViLienThongService.saveAll(entitiesToUpdate);
            donViLienThongService.saveAll(listToInsert);

            T_Log_DonViLienThong logEntry = new T_Log_DonViLienThong();
            logEntry.setNguoiThucHien(tenGoi);
            String summary = String.format("Tổng số: %d đơn vị\n- Thêm mới: %d\n-  Không thay đổi: %d",
                    totalCount, countCreated, countUnchanged);
            logEntry.setTenMuc("Đồng bộ đơn vị liên thông");
            logEntry.setSummary(summary);
            repository.save(logEntry);

            return ResponseEntity.ok().body(summary);

        } catch (Exception e) {
            log.error("Lỗi khi đồng bộ danh sách đơn vị", e);

            T_Log_DonViLienThong logEntry = new T_Log_DonViLienThong();

            logEntry.setNguoiThucHien(tenGoi);
            logEntry.setTenMuc("Đồng bộ đơn vị liên thông");
            logEntry.setSummary("Không đơn vị nào đợc đồng bộ");
            repository.save(logEntry);

            return ResponseEntity.status(500).body("Lỗi khi đồng bộ danh sách đơn vị: " + e.getMessage());
        }
    }


}
