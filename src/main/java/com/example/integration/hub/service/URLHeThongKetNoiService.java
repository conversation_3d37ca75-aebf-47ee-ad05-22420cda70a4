package com.example.integration.hub.service;

import com.example.integration.entity.T_URL_HeThongKetNoi;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;


public interface URLHeThongKetNoiService {
    T_URL_HeThongKetNoi update(T_URL_HeThongKetNoi object);

    void delete(T_URL_HeThongKetNoi object);

    Optional<T_URL_HeThongKetNoi> findById(String id);

    Page<T_URL_HeThongKetNoi>  filter(String keyword, Pageable pageable,String url,String tenMuc,String maKetNoi, Long tuNgay,Long denNgay,String orderTypes);
}
