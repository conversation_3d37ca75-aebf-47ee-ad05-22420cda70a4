package com.example.integration.hub.service.impl;

import com.example.integration.constant.Constant;
import com.example.integration.entity.T_URL_HeThongKetNoi;
import com.example.integration.hub.service.URLHeThongKetNoiService;
import com.example.integration.repository.URLHeThongKetNoiRepository;
import com.fds.flex.common.ultility.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class URLHeThongKetNoiServiceImpl implements URLHeThongKetNoiService {
    @Autowired
    private URLHeThongKetNoiRepository repository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public T_URL_HeThongKetNoi update(T_URL_HeThongKetNoi object) {
        return repository.save(object);
    }

    @Override
    public void delete(T_URL_HeThongKetNoi object) {
        repository.delete(object);
    }

    @Override
    public Optional<T_URL_HeThongKetNoi> findById(String id) {
        return repository.findById(id);
    }

    @Override
    public Page<T_URL_HeThongKetNoi> filter(String keyword, Pageable pageable,String url,String tenMuc,String maKetNoi,Long tuNgay,Long denNgay,String orderTypes) {
        Query query = new Query().with(pageable);

        List<Criteria> criteria = new ArrayList<>();

        if (Validator.isNotNull(keyword)) {

            List<Criteria> subCriterias = new ArrayList<>();
            Criteria c = Criteria.where("MaMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            c = Criteria.where("TenMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            c = Criteria.where("URL").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);

            criteria.add(new Criteria().orOperator(subCriterias));
        }

        if (Validator.isNotNull(maKetNoi)) {
            Criteria c = Criteria.where("MaKetNoi").is(maKetNoi);
            criteria.add(c);
        }
        if (Validator.isNotNull(tenMuc)) {
            Criteria c = Criteria.where("TenMuc").is(tenMuc);
            criteria.add(c);
        }
        if (Validator.isNotNull(tuNgay)) {

            Criteria c = Criteria.where("ThoiGianTao").gte(tuNgay);
            criteria.add(c);
        }

        if (Validator.isNotNull(denNgay)) {
            Criteria c = Criteria.where("ThoiGianTao").lte(denNgay);
            criteria.add(c);
        }
        if ("desc".equalsIgnoreCase(orderTypes)) {
            query.with(Sort.by(Sort.Order.desc("ThoiGianTao")));
        } else if ("asc".equalsIgnoreCase(orderTypes)) {
            query.with(Sort.by(Sort.Order.asc("ThoiGianTao")));
        }

        if (!criteria.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteria));
        }

        return PageableExecutionUtils.getPage(mongoTemplate.find(query, T_URL_HeThongKetNoi.class), pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), T_URL_HeThongKetNoi.class));
    }
    private String toLikeKeyword(String source) {
        return source.replaceAll("\\*", ".*");
    }

}
