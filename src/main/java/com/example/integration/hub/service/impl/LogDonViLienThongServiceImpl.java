package com.example.integration.hub.service.impl;

import com.example.integration.constant.Constant;
import com.example.integration.entity.T_Log_DonViLienThong;
import com.example.integration.hub.service.LogDonViLienThongService;
import com.example.integration.repository.LogDonViLienThongRepository;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.datetime.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Service
public class LogDonViLienThongServiceImpl implements LogDonViLienThongService {
    @Autowired
    private LogDonViLienThongRepository repository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public T_Log_DonViLienThong update(T_Log_DonViLienThong object) {
        return repository.save(object);
    }

    @Override
    public void delete(T_Log_DonViLienThong object) {
        repository.delete(object);
    }

    @Override
    public Optional<T_Log_DonViLienThong> findById(String id) {
        return repository.findById(id);
    }

    @Override
    public Page<T_Log_DonViLienThong> filter(String keyword, Pageable pageable,Long tuNgay,Long denNgay) {
        Query query = new Query().with(pageable);

        List<Criteria> criteria = new ArrayList<>();

        if (Validator.isNotNull(keyword)) {

            List<Criteria> subCriterias = new ArrayList<>();
            Criteria c = Criteria.where("MaMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);
            c = Criteria.where("TenMuc").regex(toLikeKeyword(keyword), Constant.INSENSITIVE);
            subCriterias.add(c);

            criteria.add(new Criteria().orOperator(subCriterias));
        }
        if (Validator.isNotNull(tuNgay)) {

            Criteria c = Criteria.where("ThoiGianTao").gte(tuNgay);
            criteria.add(c);
        }
        if (Validator.isNotNull(denNgay)) {
            Criteria c = Criteria.where("ThoiGianTao").lte(denNgay);
            criteria.add(c);
        }
        if (!criteria.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteria));
        }

        return PageableExecutionUtils.getPage(mongoTemplate.find(query, T_Log_DonViLienThong.class), pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), T_Log_DonViLienThong.class));
    }
    private String toLikeKeyword(String source) {
        return source.replaceAll("\\*", ".*");
    }

}
