package com.example.integration.hub.service;

import com.example.integration.entity.T_Log_DonViLienThong;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;


public interface LogDonViLienThongService {
    T_Log_DonViLienThong update(T_Log_DonViLienThong object);

    void delete(T_Log_DonViLienThong object);

    Optional<T_Log_DonViLienThong> findById(String id);

    Page<T_Log_DonViLienThong>  filter(String keyword, Pageable pageable,Long tuNgay,Long denNgay);
}
