package com.example.integration.hub.action.impl;

import com.example.integration.config.PropKey;
import com.example.integration.entity.InfoMail;
import com.example.integration.hub.action.SendNotifiAction;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.utility.string.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mail.MailException;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.*;

@Slf4j
@Service
public class SendNotifiActionImpl implements SendNotifiAction {
    private final int maxConnection = 10;
    private final int retry = 3;
//    private VnptProperties vnptProperties;
//    private AgencyServiceImp agencyService;
//    private KnobstickServiceImp knobstickService;
    @Autowired
    RestTemplate restTemplate;

//    String chatId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.TELEGRAM_CHANNEL_ID),
//            StringPool.BLANK);
    @Override
    public void sendNotificationTele(String message, String chatId) {
//        String redisKey = "sent_tele_messages";
//        SetOperations<String, String> setOperations = redisTemplate.opsForSet();
//        if (Boolean.TRUE.equals(setOperations.isMember(redisKey, message))) {
//            log.warn("Tin nhắn đã được gửi trước đó, không gửi lại.");
//            return;
//        }
        String botToken = GetterUtil.get(PropKey.getKeyMap().get(PropKey.TELEGRAM_BOT_TOKEN),
                StringPool.BLANK);
        try {
            String url = "https://api.telegram.org/bot" + botToken + "/sendMessage";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("chat_id", chatId);
            requestBody.put("text", message);
            requestBody.put("parse_mode", "Markdown");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);


            restTemplate.postForObject(url, requestEntity, String.class);
//            setOperations.add(redisKey, message);
        } catch (Exception e) {
            System.err.println(" Lỗi gửi tin nhắn Telegram: " + e.getMessage());
        }
    }

    @Override
    public void sendEmail(InfoMail user) throws MailException {
        if(true) {
            return;
        }
        Map<String, Object> properties = PropKey.getKeyMap();

        Properties props = new Properties();

        props.put("mail.smtp.host", GetterUtil.get(properties.get(PropKey.FLEXCORE_INTEGRATION_MAIL_HOST), StringPool.BLANK)); //SMTP Host

        props.put("mail.smtp.port", GetterUtil.get(properties.get(PropKey.FLEXCORE_INTEGRATION_MAIL_PORT), StringPool.BLANK)); //TLS Port

        props.put("mail.smtp.auth", "true"); //enable authentication
        props.put("mail.smtp.socketFactory.port", GetterUtil.get(properties.get(PropKey.FLEXCORE_INTEGRATION_MAIL_HOST), StringPool.BLANK));
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.socketFactory.fallback", "false");

//		props.put("mail.smtp.ssl.enable", "false");

        Authenticator auth = new Authenticator() {
            //override the getPasswordAuthentication method
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(GetterUtil.get(properties.get(PropKey.FLEXCORE_INTEGRATION_MAIL_USERNAME), StringPool.BLANK),
                        GetterUtil.get(properties.get(PropKey.FLEXCORE_INTEGRATION_MAIL_PASSWORD), StringPool.BLANK));
            }
        };

        Session session = Session.getInstance(props, auth);

        try {
            // Create a MimeMessage object
            MimeMessage message = new MimeMessage(session);

            // Set the sender's email address
            message.setFrom(new InternetAddress(GetterUtil.get(properties.get(PropKey.FLEXCORE_INTEGRATION_MAIL_USERNAME), StringPool.BLANK)));

            // Set the recipient's email address
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(user.getTo()));

            // Set the subject of the email
            message.setSubject(user.getSubject());

            // Set the content of the email

            message.setContent(user.getBody(), "text/html; charset=UTF-8");

            // Send the email

            //server prod chua thong nen tat tam gui mail
//            Transport.send(message);

            System.out.println("Email sent successfully!");

        } catch (javax.mail.MessagingException e) {
            e.printStackTrace();
        }
    }


}
