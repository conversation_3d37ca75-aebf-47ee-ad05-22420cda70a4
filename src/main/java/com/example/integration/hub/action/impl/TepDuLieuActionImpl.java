package com.example.integration.hub.action.impl;

import com.example.integration.constant.Constant;
import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.hub.action.TepDuLieuAction;
import com.example.integration.hub.service.TepDuLieuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
@Component
public class TepDuLieuActionImpl implements TepDuLieuAction {
    @Autowired
    private TepDuLieuService tepDuLieuService;

    public TepDuLieu uploadFile(MultipartFile file,String messagetype) {
        return tepDuLieuService.uploadFile(file, Constant.BACKUP,messagetype);
    }
    public ResponseEntity<Resource> downloadFile(String maDinhDanh) {
        return tepDuLieuService.downloadFile(maDinhDanh);
    }
    @Override
    public Page<TepDuLieu> filter(String keyword, Integer page, Integer size, String loaiNguonLuuTru_MaMuc, String orderFields, String orderTypes) {
        if (page == null || page < 0) {
            page = 0;
        }

        if (size == null || size < 0) {
            size = Constant.DEFAULT_MIN_PAGE_SIZE;
        }

        if (size > Constant.DEFAULT_MAX_PAGE_SIZE) {
            size = Constant.DEFAULT_MAX_PAGE_SIZE;
        }

        Sort sort = Sort.by(Sort.Order.desc(orderFields));
        if (orderTypes.contains("asc")) {
            sort = Sort.by(Sort.Order.asc(orderFields));
        }

        Pageable pageable = PageRequest.of(page, size, sort);

        return tepDuLieuService.filter(keyword, pageable, loaiNguonLuuTru_MaMuc);
    }
}
