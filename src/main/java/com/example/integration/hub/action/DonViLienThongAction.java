package com.example.integration.hub.action;

import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.entity.C_DonViLienThong;
import org.springframework.data.domain.Page;

import javax.validation.Valid;
import java.util.List;

public interface DonViLienThongAction {
    C_DonViLienThong add(@Valid DonViLienThongReqDTO reqDTO);

    void delete(String id);

//    List<C_DonViLienThong> filter(String keyword, Integer page, Integer size, String tenMuc, String maMuc, Long tuNgay, Long denNgay, String orderFields, String orderTypes);
    Page<C_DonViLienThong> filterSecondLevel(String keyword, Integer page, Integer size, String tenMuc, String maMuc, Long tuNgay, Long denNgay,String id,String ma, String orderFields, String orderTypes);

    C_DonViLienThong findById(String id);

    C_DonViLienThong update(String id, @Valid DonViLienThongReqDTO reqDTO);


}
