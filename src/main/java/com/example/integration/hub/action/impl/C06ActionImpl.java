package com.example.integration.hub.action.impl;

import com.example.integration.config.PortalUtil;
import com.example.integration.hub.action.C06Action;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;

@Setter
@Getter
@Service
@ConfigurationProperties(prefix = "integration.dancu")
@Slf4j
public class C06ActionImpl implements C06Action {
    @Autowired
    RestTemplate restTemplate;

    private String providerUrl;
    private String dstCode;
    private String username;
    private String secretKey;
    private String host;
    private String serviceTraCuuThongTinCongDan;

    @Override
    public String createToken(String ts) {

        String stringBeforeHash = serviceTraCuuThongTinCongDan + username + ts + secretKey;
        String token = PortalUtil.toHex256(stringBeforeHash);

        return Base64.getEncoder().encodeToString((this.username + ":" + token).getBytes());
    }

    @Override
    public String search(String token, String ts, String maTichHop, String soDinhDanh, String hoVaTen, String ngayThangNamSinh) {

        String bodyXml = PortalUtil.createSoapRequest(maTichHop, soDinhDanh, hoVaTen,
                ngayThangNamSinh, username, System.currentTimeMillis() + "");

        String url = host + serviceTraCuuThongTinCongDan + "?providerurl=" + providerUrl + "&dstcode=" + dstCode;

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", MediaType.APPLICATION_XML_VALUE);
        headers.set("Timestamp", ts);
        headers.set("Authorization", "Basic " + token);

        HttpEntity<String> entity = new HttpEntity<>(bodyXml, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

        return response.getBody();
    }
}
