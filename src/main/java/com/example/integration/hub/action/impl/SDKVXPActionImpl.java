package com.example.integration.hub.action.impl;

import com.example.integration.config.PropKey;
import com.example.integration.hub.action.SDKVXPAction;
import com.fds.flex.common.ultility.GetterUtil;
import com.vpcp.services.AgencyServiceImp;
import com.vpcp.services.KnobstickServiceImp;
import com.vpcp.services.VnptProperties;
import com.vpcp.services.model.*;
import com.vpcp.services.request.AgencyRequest;
import lombok.extern.slf4j.Slf4j;
import com.example.integration.constant.Constant;

@Slf4j

public class SDKVXPActionImpl implements SDKVXPAction {
    private final int maxConnection = 10;
    private final int retry = 3;
    private VnptProperties vnptProperties;
    private AgencyServiceImp agencyService;
    private KnobstickServiceImp knobstickService;
    StringBuffer stringBuffer;
    private String getJsonHeader(int run, String serviceType, String messageType, String docId,
                                 String fromUnit, String status) {
        String json = "";
        switch (run) {
            case Constant.RUN_GET_LIST_SENT_EDOC:
                stringBuffer = new StringBuffer();
                stringBuffer.append("{");
                stringBuffer.append("\"servicetype\":\""+serviceType+"\"");
                stringBuffer.append(",\"messagetype\":\""+ messageType +"\"");
                stringBuffer.append(",\"docId\":\""+ docId +"\"");
                stringBuffer.append("}");
                json = stringBuffer.toString();
                break;
            case Constant.RUN_GET_LIST_EDOC:
                stringBuffer = new StringBuffer();
                stringBuffer.append("{");
                stringBuffer.append("\"servicetype\":\""+serviceType+"\"");
                stringBuffer.append(",\"messagetype\":\""+ messageType +"\"");
                stringBuffer.append("}");
                json = stringBuffer.toString();

                break;
            case Constant.RUN_GET_EDOC:
                stringBuffer = new StringBuffer();
                String filePath = GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_SAVE_DIR));
                stringBuffer.append("{");
                stringBuffer.append("\"filePath\":\""+filePath+"\"");
                stringBuffer.append(",\"docId\":\""+ docId +"\"");
                stringBuffer.append("}");
                json = stringBuffer.toString();
                break;
            case Constant.RUN_SEND_EDOC:
                stringBuffer = new StringBuffer();
                stringBuffer.append("{");
                stringBuffer.append("\"from\":\""+fromUnit+"\"");
                stringBuffer.append(",\"servicetype\":\""+serviceType+"\"");
                stringBuffer.append(",\"messagetype\":\""+ messageType +"\"");
                stringBuffer.append("}");
                json = stringBuffer.toString();

                break;
            case Constant.RUN_UPDATE_STATUS:
                stringBuffer = new StringBuffer();
                stringBuffer.append("{");
                stringBuffer.append("\"status\":\""+ status +"\"");
                stringBuffer.append(",\"docid\":"+ docId );
                stringBuffer.append("}");
                json = stringBuffer.toString();
                break;
            case Constant.RUN_GET_AGENCY_LIST:
                stringBuffer = new StringBuffer();
                stringBuffer.append("{}");
                json = stringBuffer.toString();

                break;
            case Constant.RUN_REGISTER_AGENCY:
                stringBuffer = new StringBuffer();
                stringBuffer.append("{}");
                json = stringBuffer.toString();

                break;
            default:
                break;
        }
        return json;
    }

    public SDKVXPActionImpl(String endpoint, String systemId, String secret) {
        this.vnptProperties = new VnptProperties(endpoint, systemId, secret,
                GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_SDK_DIR)), maxConnection, retry);
        this.agencyService  = new AgencyServiceImp(this.vnptProperties);
        this.knobstickService = new KnobstickServiceImp(this.vnptProperties);
    }

    @Override
    public GetReceivedEdocResult getListEdoc(String serviceType, String messageType) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_GET_LIST_EDOC, serviceType, messageType, null, null, null);
        GetReceivedEdocResult result =  knobstickService.getReceivedEdocList(jsonHeader);

        return result;



    }

    @Override
    public GetSendEdocResult getListSentEdoc(String serviceType, String messageType, String docId) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_GET_LIST_SENT_EDOC, serviceType, messageType, docId, null, null);
        return knobstickService.getSentEdocList(jsonHeader);
    }

    @Override
    public GetEdocResult getEdoc(String docId) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_GET_EDOC, null, null, docId, null, null);
        return knobstickService.getEdoc(jsonHeader);
    }

    @Override
    public SendEdocResult sendEdoc(String from, String serviceType, String messageType, String urlLocalFile) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_SEND_EDOC, serviceType, messageType, null, from, null);
        return  knobstickService.sendEdoc(jsonHeader, urlLocalFile);
    }

    @Override
    public GetChangeStatusResult updateStatus(String status, String docId) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_UPDATE_STATUS, null, null, docId, null, status);
        return  knobstickService.updateStatus(jsonHeader);
    }

    @Override
    public GetAgenciesResult getListAgencies(String serviceType, String messageType, String docId) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_GET_AGENCY_LIST, null, null, null, null, null);
        return agencyService.getAgenciesList(jsonHeader);
    }

    @Override
    public RegisterAgencyResult registerAgency(AgencyRequest data) {
        String jsonHeader = this.getJsonHeader(Constant.RUN_REGISTER_AGENCY, null, null, null, null, null);
        return agencyService.registerAgency(jsonHeader, String.valueOf(data));
    }
    @Override
    public GetAgenciesResult getAgenciesList() {
        String jsonHeader = this.getJsonHeader(Constant.RUN_GET_AGENCY_LIST, null, null, null, null, null);
        return agencyService.getAgenciesList(jsonHeader);
    }
}
