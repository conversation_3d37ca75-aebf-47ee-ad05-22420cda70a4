package com.example.integration.hub.action.impl;

import com.example.integration.base.entity.TrangThaiDuLieu;
import com.example.integration.constant.Constant;
import com.example.integration.constant.MessageUtil;
import com.example.integration.constant.NotificationConstant;
import com.example.integration.dto.req.TrucTichHopReqDTO;
import com.example.integration.entity.TrucTichHop;
import com.example.integration.exception.BadRequestException;
import com.example.integration.exception.NotfoundException;
import com.example.integration.hub.action.TrucTichHopAction;
import com.example.integration.hub.service.TrucTichHopService;
import com.fds.flex.common.ultility.ResponseUtil;
import com.fds.flex.common.ultility.Validator;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.Optional;

@Component
public class TrucTichHopActionImpl implements TrucTichHopAction {

	@Autowired
	private TrucTichHopService service;

	@Autowired
	private ModelMapper modelMapper;

	@Override
	public TrucTichHop add(@Valid TrucTichHopReqDTO reqDTO) {

		TrucTichHop TrucTichHop = new TrucTichHop(false);

		modelMapper.map(reqDTO, TrucTichHop);

		TrucTichHop = service.update(TrucTichHop);

		return TrucTichHop;
	}

	@Override
	public void delete(String id) {

		if (Validator.isNull(id)) {
			throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
		}

		Optional<TrucTichHop> optional = service.findById(id);

		if (!optional.isPresent()) {
			throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
		}

		service.delete(optional.get());

	}

	@Override
	public Page<TrucTichHop> filter(String keyword, Integer page, Integer size,Long tuNgay,Long denNgay,String orderFields,
			String orderTypes) {

		if (page == null || page < 0) {
			page = 0;
		}

		if (size == null || size < 0) {
			size = Constant.DEFAULT_MIN_PAGE_SIZE;
		}

		if (size > Constant.DEFAULT_MAX_PAGE_SIZE) {
			size = Constant.DEFAULT_MAX_PAGE_SIZE;
		}

		Sort sort = Sort.by(Sort.Order.desc(orderFields));
		if (orderTypes.contains("asc")) {
			sort = Sort.by(Sort.Order.asc(orderFields));
		}

		Pageable pageable = PageRequest.of(page, size, sort);

		return service.filter(keyword,tuNgay,denNgay, pageable);
	}

	@Override
	public TrucTichHop findById(String id) {
		if (Validator.isNull(id)) {
			throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
		}

		Optional<TrucTichHop> dataSourceOpt = service.findById(id);

		if (!dataSourceOpt.isPresent()) {
			throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
		}

		return dataSourceOpt.get();
	}

	@Override
	public TrucTichHop update(String id, @Valid TrucTichHopReqDTO reqDTO) {
		if (Validator.isNull(id)) {
			throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
		}

		Optional<TrucTichHop> dataSourceOpt = service.findById(id);

		if (!dataSourceOpt.isPresent()) {
			throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
		}

		TrucTichHop TrucTichHop = dataSourceOpt.get();

		modelMapper.map(reqDTO, TrucTichHop);

		TrucTichHop = service.update(TrucTichHop);

		return TrucTichHop;
	}

	@Override
	public TrucTichHop updateTrangThaiDuLieu(String id, String maTrangThai) {
		if (Validator.isNull(id)) {
			throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
		}

		Optional<TrucTichHop> dataSourceOpt = service.findById(id);

		if (!dataSourceOpt.isPresent()) {
			throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
					NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
					MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
		}

		TrucTichHop trucTichHop = dataSourceOpt.get();

		trucTichHop.setTrangThaiDuLieu(new TrangThaiDuLieu().init(maTrangThai));

		trucTichHop = service.update(trucTichHop);

		return trucTichHop;
	}

}
