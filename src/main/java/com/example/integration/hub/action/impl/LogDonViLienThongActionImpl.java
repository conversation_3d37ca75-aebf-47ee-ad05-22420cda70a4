package com.example.integration.hub.action.impl;

import com.example.integration.base.entity.TrangThaiDuLieu;
import com.example.integration.constant.Constant;
import com.example.integration.constant.MessageUtil;
import com.example.integration.constant.NotificationConstant;
import com.example.integration.dto.req.LogDonViLienThongReqDTO;
import com.example.integration.entity.T_Log_DonViLienThong;
import com.example.integration.exception.BadRequestException;
import com.example.integration.exception.NotfoundException;
import com.example.integration.hub.action.LogDonViLienThongAction;
import com.example.integration.hub.service.LogDonViLienThongService;
import com.fds.flex.common.ultility.ResponseUtil;
import com.fds.flex.common.ultility.Validator;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Optional;

@Component
public class LogDonViLienThongActionImpl implements LogDonViLienThongAction {
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private LogDonViLienThongService service;
    @Override
    public T_Log_DonViLienThong add(LogDonViLienThongReqDTO reqDTO) {

        T_Log_DonViLienThong LogDonViLienThong = new T_Log_DonViLienThong(false);

        TrangThaiDuLieu trangThaiDuLieu = new TrangThaiDuLieu();
        trangThaiDuLieu.setTenMuc("Chính thức");
        trangThaiDuLieu.setMaMuc("02");
        LogDonViLienThong.setTrangThaiDuLieu(trangThaiDuLieu);
        modelMapper.map(reqDTO, LogDonViLienThong);

        LogDonViLienThong = service.update(LogDonViLienThong);

        return LogDonViLienThong;
    }

    @Override
    public void delete(String id) {
        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<T_Log_DonViLienThong> optional = service.findById(id);

        if (!optional.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        service.delete(optional.get());
    }

    @Override
    public Page<T_Log_DonViLienThong> filter(String keyword, Integer page, Integer size,Long tuNgay,Long denNgay, String orderFields, String orderTypes) {
        if (page == null || page < 0) {
            page = 0;
        }

        if (size == null || size < 0) {
            size = Constant.DEFAULT_MIN_PAGE_SIZE;
        }

        if (size > Constant.DEFAULT_MAX_PAGE_SIZE) {
            size = Constant.DEFAULT_MAX_PAGE_SIZE;
        }

        Sort sort = Sort.by(Sort.Order.desc(orderFields));
        if (orderTypes.contains("asc")) {
            sort = Sort.by(Sort.Order.asc(orderFields));
        }

        Pageable pageable = PageRequest.of(page, size, sort);

        return service.filter(keyword, pageable, tuNgay, denNgay);
    }

    @Override
    public T_Log_DonViLienThong findById(String id) {
        return null;
    }

    @Override
    public T_Log_DonViLienThong update(String id, LogDonViLienThongReqDTO reqDTO) {
        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<T_Log_DonViLienThong> dataSourceOpt = service.findById(id);

        if (!dataSourceOpt.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        T_Log_DonViLienThong LogDonViLienThong = dataSourceOpt.get();

        modelMapper.map(reqDTO, LogDonViLienThong);

        LogDonViLienThong = service.update(LogDonViLienThong);

        return LogDonViLienThong;
    }
}
