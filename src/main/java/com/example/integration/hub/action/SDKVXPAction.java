package com.example.integration.hub.action;

import com.vpcp.services.model.*;
import com.vpcp.services.request.AgencyRequest;

public interface SDKVXPAction {
    GetReceivedEdocResult  getListEdoc(String serviceType, String messageType);
    GetSendEdocResult getListSentEdoc(String serviceType, String messageType, String docId);
    GetEdocResult getEdoc(String docId);
    SendEdocResult sendEdoc(String from, String serviceType, String messageType, String urlLocalFile);
    GetChangeStatusResult updateStatus(String status, String docId);
    GetAgenciesResult getListAgencies(String serviceType, String messageType, String docId);
    RegisterAgencyResult registerAgency(AgencyRequest data);
    GetAgenciesResult getAgenciesList();
}
