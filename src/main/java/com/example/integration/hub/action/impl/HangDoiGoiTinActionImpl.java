package com.example.integration.hub.action.impl;

import com.example.integration.base.entity.TrangThaiDuLieu;
import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.constant.MessageUtil;
import com.example.integration.constant.NotificationConstant;
import com.example.integration.dto.req.HangDoiGoiTinReqDTO;
import com.example.integration.dto.req.HangDoiGoiTinTrangThaiReqDTO;
import com.example.integration.entity.*;
import com.example.integration.entity.ext.CauHinhGoiTinExt;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.example.integration.entity.ext.TrangThaiLienThongExt;
import com.example.integration.exception.BadRequestException;
import com.example.integration.exception.NotfoundException;
import com.example.integration.hub.action.HangDoiGoiTinAction;
import com.example.integration.hub.service.HangDoiGoiTinService;
import com.example.integration.hub.service.TepDuLieuService;
import com.example.integration.hub.service.TrucTichHopService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.ResponseUtil;
import com.fds.flex.common.ultility.Validator;

import com.fds.flex.common.utility.string.StringPool;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.*;

@Component
public class HangDoiGoiTinActionImpl implements HangDoiGoiTinAction {

    private static final Logger log = LoggerFactory.getLogger(HangDoiGoiTinActionImpl.class);
    @Autowired
    private HangDoiGoiTinService service;

    @Autowired
    private TrucTichHopService trucService;

    @Autowired
    private TepDuLieuService tepDuLieuService;

    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private HangDoiGoiTinService hangDoiGoiTinService;

    @Override
    public HangDoiGoiTin add(@Valid HangDoiGoiTinReqDTO request,HangDoiGoiTin.ContentEdoc contentEdoc) {

        String maDinhDanh = UUID.randomUUID().toString();
        request.setMaGoiTin(maDinhDanh);

        TrucTichHop trucTichHop = trucService.findAll().get(0);
        if (Validator.isNull(trucTichHop)) {
            throw new NotfoundException(ResponseUtil.RespCode.DATA_INCORRECT.getCode(),
                    "Truc tich hop not found",
                    "Truc tich hop not found");
        }


        HangDoiGoiTin hangDoiGoiTin = new HangDoiGoiTin(false);
        modelMapper.map(request, hangDoiGoiTin);

        List<HeThongKetNoiExt> heThongKetNoiExts = trucTichHop.getHeThongKetNoi();

        hangDoiGoiTin.setNoiNhanGoiTin(new ArrayList<>());

        List<HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi> noiNhanGoiTins = request.getNoiNhanGoiTin();

        boolean isNoiNganh;

        for (HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi noiNhanGoiTin : noiNhanGoiTins) {
            isNoiNganh = false;
            for (HeThongKetNoiExt heThongKetNoiExt : heThongKetNoiExts) {
                if (noiNhanGoiTin.getMaKetNoi().equals(heThongKetNoiExt.getMaKetNoi())) {
                    isNoiNganh = true;
                    hangDoiGoiTin.getNoiNhanGoiTin().add(heThongKetNoiExt);
                    break;
                }
            }

            if (!isNoiNganh) {
                //Don vi nhan ngoai nganh
                HeThongKetNoiExt heThongKetNoiNgoaiNganh = new HeThongKetNoiExt();
                HeThongKetNoiExt.GiaoThucKetNoi giaoThucKetNoiServer = new HeThongKetNoiExt.GiaoThucKetNoi();
                giaoThucKetNoiServer.setMaMuc("02");
                giaoThucKetNoiServer.setTenMuc("QLVB_SERVER");

                heThongKetNoiNgoaiNganh.setGiaoThucKetNoi(giaoThucKetNoiServer);
                heThongKetNoiNgoaiNganh.setMaKetNoi(noiNhanGoiTin.getMaKetNoi());
                heThongKetNoiNgoaiNganh.setTenKetNoi(noiNhanGoiTin.getTenKetNoi());

                hangDoiGoiTin.getNoiNhanGoiTin().add(heThongKetNoiNgoaiNganh);
            }
        }

        if (Validator.isNotNull(request.getTepDuLieu().getMaDinhDanh())) {
            Optional<TepDuLieu> tepDuLieu = tepDuLieuService.findByMaDinhDanh(request.getTepDuLieu().getMaDinhDanh());
            if (tepDuLieu.isPresent()) {
                hangDoiGoiTin.setNoiDungGoiTin(tepDuLieu.get());
            }
        }

        hangDoiGoiTin.setMaDinhDanh(maDinhDanh);
        if(Validator.isNotNull(contentEdoc)) {
            hangDoiGoiTin.setContentEdoc(contentEdoc);
        }
        hangDoiGoiTin = service.update(hangDoiGoiTin);
        hangDoiGoiTin.getNoiDungGoiTin().setDuongDanURL("----");

        return hangDoiGoiTin;
    }

    @Override
    public void delete(String id) {

        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<HangDoiGoiTin> optional = service.findById(id);

        if (!optional.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        service.delete(optional.get());

    }

    @Override
    public Page<HangDoiGoiTin> filter(String keyword, String trucTichHop, String trangthai,
                                      String noiGui, String noiNhan, String dinhDangGoiTin, String kieuLoaiGoiTin,String trangThaiLienThong_MaMuc,String tieuDe, String soKyHieu,String loaiGoiTin,String trangThaiVanBan,String kieuVanBan,
                                      Long tuNgay,Long denNgay, Integer page,
                                      Integer size, String orderFields, String orderTypes) {

        if (page == null || page < 0) {
            page = 0;
        }

        if (size == null || size < 0) {
            size = Constant.DEFAULT_MIN_PAGE_SIZE;
        }

        if (size > Constant.DEFAULT_MAX_PAGE_SIZE) {
            size = Constant.DEFAULT_MAX_PAGE_SIZE;
        }

//		Sort sort = Sort.by(Sort.Order.desc(orderFields));
//		if (orderTypes.contains("asc")) {
//			sort = Sort.by(Sort.Order.asc(orderFields));
//		}

        Pageable pageable = PageRequest.of(page, size);

        return service.filter(keyword, trucTichHop, trangthai, noiGui, noiNhan, dinhDangGoiTin, kieuLoaiGoiTin,trangThaiLienThong_MaMuc,tieuDe,soKyHieu,loaiGoiTin,trangThaiVanBan,kieuVanBan,tuNgay,denNgay, pageable);
    }

    @Override
    public Page<HangDoiGoiTin> filter(String trucTichHop, String kieuLoaiGoiTin, String trangthai, Long tuNgay, Long denNgay, Integer page, Integer size, String orderFields, String orderTypes) {
        Pageable pageable = PageRequest.of(page, size);

        return service.filter(trucTichHop, kieuLoaiGoiTin, trangthai, tuNgay, denNgay, orderFields, orderTypes, pageable);
    }

    @Override
    public HangDoiGoiTin findById(String id) {
        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<HangDoiGoiTin> dataSourceOpt = service.findById(id);

        if (!dataSourceOpt.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        return dataSourceOpt.get();
    }

    @Override
    public HangDoiGoiTin findByMaGoiTin(String maGoiTin) {
        Optional<HangDoiGoiTin> dataSourceOpt = service.findByMaGoiTin(maGoiTin);

        if (!dataSourceOpt.isPresent()) {
            log.warn("Not found hang doi goi tin with magoitin: " + maGoiTin);
            return null;
        }

        return dataSourceOpt.get();
    }

    @Override
    public HangDoiGoiTin update(String id, @Valid HangDoiGoiTinTrangThaiReqDTO reqDTO) throws Exception {
        if (Validator.isNull(reqDTO.getTrangThai()) || Validator.isNull(reqDTO.getNoiNhanGoiTin().getMaKetNoi())) {
            throw new NotfoundException(ResponseUtil.RespCode.DATA_INCORRECT.getCode(),
                    "getTrangThai || getMaKetNoi not found",
                    "getTrangThai || getMaKetNoi not found");
        }

        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<HangDoiGoiTin> dataSourceOpt = service.findById(id);

        if (!dataSourceOpt.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        HangDoiGoiTin hangDoiGoiTin = dataSourceOpt.get();
        TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt(reqDTO.getTrangThai());

//		List<HeThongKetNoiExt> noiNhanGoiTins = hangDoiGoiTin.getNoiNhanGoiTin();

        for (HeThongKetNoiExt noiNhan : hangDoiGoiTin.getNoiNhanGoiTin()) {
            if (noiNhan.getMaKetNoi().equals(reqDTO.getNoiNhanGoiTin().getMaKetNoi())) {
                noiNhan.setTrangThaiLienThong(trangThaiLienThongExt);
                noiNhan.setThoiGianKetNoi(System.currentTimeMillis());
                service.update(hangDoiGoiTin);
                break;
            }
        }
        return null;
    }

    @Override
    public HangDoiGoiTin update(String id, @Valid HangDoiGoiTinReqDTO reqDTO) {
        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<HangDoiGoiTin> dataSourceOpt = service.findById(id);

        if (!dataSourceOpt.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        HangDoiGoiTin HangDoiGoiTin = dataSourceOpt.get();

        modelMapper.map(reqDTO, HangDoiGoiTin);

        HangDoiGoiTin = service.update(HangDoiGoiTin);

        return HangDoiGoiTin;
    }

    @Override
    public HangDoiGoiTin updateTrangThai(String docId, String newStatus, String noiNhanRequest) {
        if (Validator.isNull(docId)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<HangDoiGoiTin> dataSourceOpt = service.findByMaGoiTin(docId);

        if (!dataSourceOpt.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }
        HangDoiGoiTin hangDoiGoiTin = dataSourceOpt.get();

        if (Validator.isNull(hangDoiGoiTin.getNoiNhanGoiTin()) || hangDoiGoiTin.getNoiNhanGoiTin().isEmpty()) {
            return null;
        }

        for (HeThongKetNoiExt noiNhanGoiTin : hangDoiGoiTin.getNoiNhanGoiTin()) {
            if (noiNhanGoiTin.getMaKetNoi().equals(noiNhanRequest)) {
                try {
                    String oldStatus = noiNhanGoiTin.getTrangThaiLienThong().getMaMuc();
                    if (newStatus.equals(Constant.STATUS_INITIAL) || oldStatus.equals(Constant.STATUS_DONE)
                            || oldStatus.equals(Constant.STATUS_FAIL)) {
                        //trang thai moi ko the la init, va ho so da hoan thanh or fail thi ko the cap nhat
                        throw new Exception("Status invalid");
                    }

                    TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt(newStatus);
                    noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                    return service.update(hangDoiGoiTin);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    @Override
    public TepDuLieu uploadFile(MultipartFile file, String loaiNguonDuLieu,String messagetype) {
        return tepDuLieuService.uploadFile(file, loaiNguonDuLieu,messagetype);
    }


    @Override
    public File viewFile(String maDinhDanh) {
        return tepDuLieuService.viewFile(maDinhDanh);
    }

    @Override
    public ThongKeEdoc viewThongKe(Integer year) {
        List<MonthlySummary> result = tepDuLieuService.getSummaryByYearAndSourceType(year, Constant.GET_EDOC);

        List<MonthlySummary> resultFull = tepDuLieuService.getSummaryByYearAndMultiSourceType(year, Arrays.asList(Constant.GET_EDOC, Constant.SEND_EDOC));

        result.forEach(summary -> {
            summary.setFormattedSize(formatSize(summary.getTotalSize()));
			summary.setTotalCount(null);
        });
        resultFull.forEach(summary -> {
			summary.setFormattedSize(formatSize(summary.getTotalSize()));
			summary.setTotalCount(null);
		});

        ThongKeEdoc thongke = new ThongKeEdoc();
        File disk = new File(System.getProperty("user.dir"));

        long totalSize = resultFull.stream()
                .mapToLong(MonthlySummary::getTotalSize)
                .sum();

        long totalSpace = disk.getTotalSpace();
        double percentUsed = ((double) totalSize / totalSpace) * 100;
        thongke.setEdocByMonth(result);
        thongke.setHosoByMonth(resultFull);
        thongke.setHardDisk(new ThongKeEdoc.HardDisk(formatSize(totalSpace), formatSize(totalSize), adjustPercent(percentUsed)));
        return thongke;
    }

    static final List<String> kieuLoaiGoiTinRequired = Arrays.asList(Constant.GET_EDOC, Constant.SEND_EDOC);

    @Override
    public ThongKeEdoc getStatisticNoiGuiGoiTin(String kieuLoaiGoiTin, int year) {
        if (!kieuLoaiGoiTinRequired.contains(kieuLoaiGoiTin)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.KIEU_LOAI_GOI_TIN_INCORRECT,
                    MessageUtil.responseMessage(NotificationConstant.KIEU_LOAI_GOI_TIN_INCORRECT));
        }

        List<MonthlySummary> result = hangDoiGoiTinService.getSummaryByYearAndKieuLoaiGoiTin(kieuLoaiGoiTin, year);

        ThongKeEdoc thongke = new ThongKeEdoc();
        thongke.setEdocByMonth(result);
        thongke.setHosoByMonth(null);
        thongke.setHardDisk(null);
        return thongke;
    }

    @Override
    public ThongKeLienThongVanBan getStatisticThongKeLienThongVanBan(int year) {


        List<MonthlySummary> khoiLuongNhan = tepDuLieuService.getSummaryByYearAndSourceType(year, Constant.GET_EDOC);
        List<MonthlySummary> khoiLuongGui = tepDuLieuService.getSummaryByYearAndSourceType(year, Constant.SEND_EDOC);
        ThongKeLienThongVanBan.TiLeVanBan tiLeVanBanNhan = hangDoiGoiTinService.thongKeTiLeVanBanNhan(year);
        ThongKeLienThongVanBan.TiLeVanBan tiLeVanBanGui = hangDoiGoiTinService.thongKeTiLeVanBanGui(year);


        khoiLuongNhan.forEach(summary -> summary.setFormattedSize(formatSize(summary.getTotalSize())));
        khoiLuongGui.forEach(summary -> summary.setFormattedSize(formatSize(summary.getTotalSize())));

        ThongKeLienThongVanBan thongKeLienThongVanBan = new ThongKeLienThongVanBan();
        thongKeLienThongVanBan.setKhoiLuongVanBanGui(khoiLuongGui);
        thongKeLienThongVanBan.setKhoiLuongVanBanNhan(khoiLuongNhan);
        thongKeLienThongVanBan.setTiLeNhan(tiLeVanBanNhan);
        thongKeLienThongVanBan.setTiLeGui(tiLeVanBanGui);

        return thongKeLienThongVanBan;
    }

    public static double adjustPercent(double percent) {
        // Kiểm tra nếu percent nhỏ hơn 0.001 (tương đương 0.1%)
        if (percent * 100 < 0.1) {
            return 0.1; // Mặc định trả về 0.1%
        }
        // Nếu lớn hơn 0.1%, giữ nguyên giá trị
        return percent * 100;
    }

    public static String formatSize(long bytes) {
        double sizeInMB = (double) bytes / (1024 * 1024);
        double sizeInGB = sizeInMB / 1024;

        if (sizeInGB >= 1) {
            return String.format("%.2f GB", sizeInGB);
        } else {
            return String.format("%.2f MB", sizeInMB);
        }
    }

}
