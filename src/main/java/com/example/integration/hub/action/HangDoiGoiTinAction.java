package com.example.integration.hub.action;

import com.example.integration.dto.req.HangDoiGoiTinReqDTO;
import com.example.integration.dto.req.HangDoiGoiTinTrangThaiReqDTO;
import com.example.integration.entity.HangDoiGoiTin;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.entity.ThongKeEdoc;
import com.example.integration.entity.ThongKeLienThongVanBan;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.util.Map;

public interface HangDoiGoiTinAction {
	HangDoiGoiTin add(@Valid HangDoiGoiTinReqDTO reqDTO,HangDoiGoiTin.ContentEdoc contentEdoc);

	void delete(String id);

	Page<HangDoiGoiTin> filter(String keyword, String trucTichHop, String trangthai,String noiGui, String noiNhan, String dinhDangGoiTin, String kieuLoaiGoiTin,String trangThaiLienThong_MaMuc,String tieuDe, String soKyHieu,String loaiGoiTin,String trangThaiVanBan,String kieuVanBan,Long tuNgay,Long denNgay, Integer page, Integer size,
			String orderFields, String orderTypes);

	Page<HangDoiGoiTin> filter(String trucTichHop, String kieuLoaiGoiTin, String trangthai, Long tuNgay, Long denNgay, Integer page,
							   Integer size, String orderFields, String orderTypes);

	HangDoiGoiTin findById(String id);
	HangDoiGoiTin findByMaGoiTin(String maGoiTin);

	HangDoiGoiTin update(String id, @Valid HangDoiGoiTinReqDTO reqDTO);
	HangDoiGoiTin update(String id, @Valid HangDoiGoiTinTrangThaiReqDTO reqDTO) throws Exception;

	HangDoiGoiTin updateTrangThai(String id, String maTrangThai, String noiNhanRequest);

	TepDuLieu uploadFile(MultipartFile file, String loaiNguonDuLieu,String messagetype);
	File viewFile(String maDinhDanh);

	ThongKeEdoc viewThongKe(Integer year);

	ThongKeEdoc getStatisticNoiGuiGoiTin(String kieuLoaiGoiTin, int year);
	ThongKeLienThongVanBan getStatisticThongKeLienThongVanBan(int year);
}
