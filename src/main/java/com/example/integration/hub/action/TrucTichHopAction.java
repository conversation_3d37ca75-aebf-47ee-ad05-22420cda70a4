package com.example.integration.hub.action;


import javax.validation.Valid;
import com.example.integration.dto.req.TrucTichHopReqDTO;
import com.example.integration.entity.TrucTichHop;
import org.springframework.data.domain.Page;

public interface TrucTichHopAction {
	TrucTichHop add(@Valid TrucTichHopReqDTO reqDTO);

	void delete(String id);

	Page<TrucTichHop> filter(String keyword, Integer page, Integer size,Long tuNgay, Long denNgay, String orderFields, String orderTypes);

	TrucTichHop findById(String id);

	TrucTichHop update(String id, @Valid TrucTichHopReqDTO reqDTO);

	TrucTichHop updateTrangThaiDuLieu(String id, String maTrangThai);
}
