package com.example.integration.hub.action.impl;

import com.example.integration.constant.Constant;
import com.example.integration.constant.MessageUtil;
import com.example.integration.constant.NotificationConstant;
import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.exception.BadRequestException;
import com.example.integration.exception.NotfoundException;
import com.example.integration.hub.action.DonViLienThongAction;
import com.example.integration.hub.service.DonViLienThongService;
import com.fds.flex.common.ultility.ResponseUtil;
import com.fds.flex.common.ultility.Validator;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
@Component
public class DonViLienThongActionImpl implements DonViLienThongAction {
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private DonViLienThongService service;
    @Override
    public C_DonViLienThong add(DonViLienThongReqDTO reqDTO) {

        C_DonViLienThong donViLienThong = new C_DonViLienThong(false);


        modelMapper.map(reqDTO, donViLienThong);

        donViLienThong = service.update(donViLienThong);

        return donViLienThong;
    }

    @Override
    public void delete(String id) {
        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<C_DonViLienThong> optional = service.findById(id);

        if (!optional.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        service.delete(optional.get());
    }

//    @Override
//    public Page<DonViLienThongReqDTO> filter(String keyword, Integer page, Integer size, String tenMuc, String maMuc, Long tuNgay, Long denNgay, String orderFields, String orderTypes) {
//        if (page == null || page < 0) {
//            page = 0;
//        }
//
//        if (size == null || size < 0) {
//            size = Constant.DEFAULT_MIN_PAGE_SIZE;
//        }
//
//        if (size > Constant.DEFAULT_MAX_PAGE_SIZE) {
//            size = Constant.DEFAULT_MAX_PAGE_SIZE;
//        }
//
//        Sort sort = Sort.by(Sort.Order.desc(orderFields));
//        if (orderTypes.contains("asc")) {
//            sort = Sort.by(Sort.Order.asc(orderFields));
//        }
//
//        Pageable pageable = PageRequest.of(page, size, sort);
//
//        return service.filter(keyword, pageable, tenMuc, maMuc,tuNgay,denNgay);
//    }
    @Override
    public Page<C_DonViLienThong> filterSecondLevel(String keyword, Integer page, Integer size, String tenMuc, String maMuc, Long tuNgay, Long denNgay,String id,String ma, String orderFields, String orderTypes) {
        if (page == null || page < 0) {
            page = 0;
        }

        if (size == null || size < 0) {
            size = Constant.DEFAULT_MIN_PAGE_SIZE;
        }

        if (size > Constant.DEFAULT_MAX_PAGE_SIZE) {
            size = Constant.DEFAULT_MAX_PAGE_SIZE;
        }

        Sort sort = Sort.by(Sort.Order.desc(orderFields));
        if (orderTypes.contains("asc")) {
            sort = Sort.by(Sort.Order.asc(orderFields));
        }

        Pageable pageable = PageRequest.of(page, size, sort);

        return service.filterSecondLevel(keyword, pageable, tenMuc, maMuc,tuNgay,denNgay,id,ma);
    }
    @Override
    public C_DonViLienThong findById(String id) {
        return null;
    }

    @Override
    public C_DonViLienThong update(String id, DonViLienThongReqDTO reqDTO) {
        if (Validator.isNull(id)) {
            throw new BadRequestException(ResponseUtil.RespCode.FIELD_NULL_OR_EMPRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_ID_NULL_EMPTY));
        }

        Optional<C_DonViLienThong> dataSourceOpt = service.findById(id);

        if (!dataSourceOpt.isPresent()) {
            throw new NotfoundException(ResponseUtil.RespCode.NOT_FOUND_ENTRY_ERROR.getCode(),
                    NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND,
                    MessageUtil.responseMessage(NotificationConstant.DATA_SOURCE_DOCUMENT_NOT_FOUND));
        }

        C_DonViLienThong donViLienThong = dataSourceOpt.get();

        modelMapper.map(reqDTO, donViLienThong);

        donViLienThong = service.update(donViLienThong);

        return donViLienThong;
    }


}
