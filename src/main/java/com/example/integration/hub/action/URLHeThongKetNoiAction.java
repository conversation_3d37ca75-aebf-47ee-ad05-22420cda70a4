package com.example.integration.hub.action;

import com.example.integration.dto.req.URLHeThongKetNoiReqDTO;
import com.example.integration.entity.T_URL_HeThongKetNoi;
import org.springframework.data.domain.Page;

import javax.validation.Valid;

public interface URLHeThongKetNoiAction {
    T_URL_HeThongKetNoi add(@Valid URLHeThongKetNoiReqDTO reqDTO);

    void delete(String id);

    Page<T_URL_HeThongKetNoi> filter(String keyword, Integer page, Integer size,String url,String tenMuc,String maKetNoi,Long tuNgay,Long denNgay, String orderFields, String orderTypes);

    T_URL_HeThongKetNoi findById(String id);

    T_URL_HeThongKetNoi update(String id, @Valid URLHeThongKetNoiReqDTO reqDTO);


}
