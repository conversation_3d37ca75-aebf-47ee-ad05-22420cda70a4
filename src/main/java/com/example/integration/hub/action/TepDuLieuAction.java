package com.example.integration.hub.action;

import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.entity.TepDuLieu;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface TepDuLieuAction {
    TepDuLieu uploadFile(MultipartFile file,String messagetype);
    Page<TepDuLieu> filter(String keyword, Integer page, Integer size, String loaiNguonLuuTru_MaMuc, String orderFields, String orderTypes);
    ResponseEntity<Resource> downloadFile(String maDinhDanh);
}
