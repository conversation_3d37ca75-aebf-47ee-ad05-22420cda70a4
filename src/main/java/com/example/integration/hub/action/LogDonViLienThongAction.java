package com.example.integration.hub.action;

import com.example.integration.dto.req.LogDonViLienThongReqDTO;
import com.example.integration.entity.T_Log_DonViLienThong;
import org.springframework.data.domain.Page;

import javax.validation.Valid;

public interface LogDonViLienThongAction {
    T_Log_DonViLienThong add(@Valid LogDonViLienThongReqDTO reqDTO);

    void delete(String id);

    Page<T_Log_DonViLienThong> filter(String keyword, Integer page, Integer size,Long tuNgay,Long denNgay, String orderFields, String orderTypes);

    T_Log_DonViLienThong findById(String id);

    T_Log_DonViLienThong update(String id, @Valid LogDonViLienThongReqDTO reqDTO);


}
