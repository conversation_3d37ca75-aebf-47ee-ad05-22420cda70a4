package com.example.integration.hub.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Utility class to count function calls and save/load counts to/from a file
 */
@Component
@Slf4j
public class FunctionCallCounter {
    private static final String COUNT_FILE_PATH = "function_call_counts.txt";
    private final Map<String, AtomicInteger> functionCounts = new ConcurrentHashMap<>();
    
    // Function types
    public static final String FUNCTION_GET = "GET";
    public static final String FUNCTION_SEND = "SEND";
    public static final String FUNCTION_UPDATE = "UPDATE";
    
    /**
     * Increment the count for a specific function type
     * @param functionType The type of function (GET, SEND, UPDATE)
     */
    public void incrementCount(String functionType) {
        functionCounts.computeIfAbsent(functionType, k -> new AtomicInteger(0)).incrementAndGet();
        saveCountsToFile();
    }
    
    /**
     * Get the current count for a specific function type
     * @param functionType The type of function (GET, SEND, UPDATE)
     * @return The current count
     */
    public int getCount(String functionType) {
        return functionCounts.getOrDefault(functionType, new AtomicInteger(0)).get();
    }
    
    /**
     * Get all current counts
     * @return A map of function types to counts
     */
    public Map<String, Integer> getAllCounts() {
        Map<String, Integer> result = new HashMap<>();
        for (Map.Entry<String, AtomicInteger> entry : functionCounts.entrySet()) {
            result.put(entry.getKey(), entry.getValue().get());
        }
        return result;
    }
    
    /**
     * Reset all counts to zero
     */
    public void resetCounts() {
        functionCounts.clear();
        saveCountsToFile();
    }
    
    /**
     * Save the current counts to a file
     */
    private void saveCountsToFile() {
        try (FileWriter writer = new FileWriter(COUNT_FILE_PATH)) {
            for (Map.Entry<String, AtomicInteger> entry : functionCounts.entrySet()) {
                writer.write(entry.getKey() + ":" + entry.getValue().get() + "\n");
            }
        } catch (IOException e) {
            log.error("Error saving function call counts to file", e);
        }
    }
    
    /**
     * Load counts from the file
     */
    public void loadCountsFromFile() {
        File file = new File(COUNT_FILE_PATH);
        if (!file.exists()) {
            return;
        }
        
        try {
            Files.lines(Paths.get(COUNT_FILE_PATH)).forEach(line -> {
                String[] parts = line.split(":");
                if (parts.length == 2) {
                    String functionType = parts[0];
                    int count = Integer.parseInt(parts[1]);
                    functionCounts.put(functionType, new AtomicInteger(count));
                }
            });
        } catch (IOException e) {
            log.error("Error loading function call counts from file", e);
        }
    }
}
