package com.example.integration.repository;

import com.example.integration.entity.C_DonViLienThong;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface DonViLienThongRepository extends MongoRepository<C_DonViLienThong,String> {
    @Query("{$and: [{'MaMuc': ?0},{'TrangThaiDuLieu.MaMuc': '?1'}]}")
    Optional<C_DonViLienThong> findByMaMuc(String maDinhDanh,String trangThai_MaMuc);
    @Query("{$and:[{'MaMuc': {$in: ?0}}, {'TrangThaiDuLieu.MaMuc': ?1}]}")
    List<C_DonViLienThong> findByMaMucIn(List<String> maMucListm, String trangThai);

    Page<C_DonViLienThong> findAll(Pageable pageable);
    @Query("{'MaMuc': {$nin: ?0}}")
    List<C_DonViLienThong> findByMaMucNotIn(List<String> maMucList);
}
