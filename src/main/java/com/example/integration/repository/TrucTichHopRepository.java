package com.example.integration.repository;

import com.example.integration.entity.TrucTichHop;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface TrucTichHopRepository extends MongoRepository<TrucTichHop, String> {
    @Query("{ 'HeThongKetNoi.MaKetNoi': ?0, 'MaMuc': ?1 }")
    Optional<TrucTichHop> findByHeThongKetNoi_MaKetNoiAndMaMuc(String maKetNoi, String maMuc);
}
	