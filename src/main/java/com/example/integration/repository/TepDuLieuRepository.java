package com.example.integration.repository;

import com.example.integration.entity.MonthlySummary;
import com.example.integration.entity.TepDuLieu;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


@Repository
@Transactional
public interface TepDuLieuRepository extends MongoRepository<TepDuLieu, String> {
    Optional<TepDuLieu> findByMaDinhDanh(String maDinhDanh);

    Optional<TepDuLieu> findByDuongDanURL(String duongDanURL);

    @Aggregation(pipeline = {
            "{ $match: { 'Year': ?0, 'LoaiNguonLuuTru.MaMuc': ?1,'DinhDangGoiTin': 'edoc' } }",
            "{ $group: { '_id': '$Month' , totalSize: { $sum: { $toLong: '$KichThuocTep' } }, totalCount: { $sum: 1 } } }",
            "{ $sort: { '_id': 1 } }"
    })
    List<MonthlySummary> getSummaryByYearAndSourceType(int year, String loaiDuLieu);

    @Aggregation(pipeline = {
            "{ $match: { 'Year': ?0, 'LoaiNguonLuuTru.MaMuc': { $in: ?1 },'DinhDangGoiTin': 'edoc' } }",
            "{ $group: { '_id': '$Month' , totalSize: { $sum: { $toLong: '$KichThuocTep' } }, totalCount: { $sum: 1 } } }",
            "{ $sort: { '_id': 1 } }"
    })
    List<MonthlySummary> getSummaryByYearAndMultiSourceType(int year, List<String> loaiDuLieu);
}
