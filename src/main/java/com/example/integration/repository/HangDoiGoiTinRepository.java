package com.example.integration.repository;

import com.example.integration.entity.HangDoiGoiTin;
import com.example.integration.entity.MonthlySummary;
import com.example.integration.entity.ThongKeLienThongVanBan;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface HangDoiGoiTinRepository extends MongoRepository<HangDoiGoiTin, String> {
    Optional<HangDoiGoiTin> findByMaDinhDanh(String maDinhDanh);

    Optional<HangDoiGoiTin> findByMaGoiTin(String maGoiTin);

    @Query("{ 'KieuLoaiGoiTin': ?0, 'MaPhienBan': ?1, 'NoiNhanGoiTin.TrangThaiLienThong.MaMuc' :  ?2}")
    List<HangDoiGoiTin> findByKieuLoaiGoiTinAndMaPhienBanAndTrangThai(String kieuLoaiGoiTin, String maPhienBan, String trangThai);

    @Query("{ 'KieuLoaiGoiTin': ?0, 'NoiGuiGoiTin.ThoiGianGui': { $gt: ?1 } }")
    List<HangDoiGoiTin> findByKieuLoaiGoiTinAndThoiGianGui(String kieuLoaiGoiTin, long thoiGianGui);

    @Query("{ 'KieuLoaiGoiTin': ?0, 'NoiNhanGoiTin.TrangThaiLienThong.MaMuc': ?1, 'NoiGuiGoiTin.ThoiGianGui': { $lt: ?2 } }")
    List<HangDoiGoiTin> findByKieuLoaiGoiTinAndTrangThaiAndThoiGianGuiLessThan(String kieuLoaiGoiTin, String trangThai, long thoiGianGui);

    @Aggregation(pipeline = {
            "{$match : {'KieuLoaiGoiTin': ?0}}",
            "{ $project: { year: { $year: { $toDate: { $multiply: ['$NoiGuiGoiTin.ThoiGianGui'] } } }, month: { $month: { $toDate: { $multiply: ['$NoiGuiGoiTin.ThoiGianGui'] } } } } }",
            "{ $match: { year: ?1 } }",
            "{ $group: { _id: '$month', totalCount: { $sum: 1 } } }",
    })
    List<MonthlySummary> getSummaryByYearAndKieuLoaiGoiTin(String kieuLoaiGoiTin, int year);

    @Aggregation(pipeline = {
            "{ $match: { KieuLoaiGoiTin: 'getEdoc', DinhDangGoiTin: 'edoc' } }",
            "{ $addFields: { year: { $year: { $toDate: { $multiply: ['$NoiGuiGoiTin.ThoiGianGui', 1] } } } } }",
            "{ $match: { year: ?0 } }",
            "{ $addFields: { nguoiNhanDauTien: { $arrayElemAt: ['$NoiNhanGoiTin', 0] } } }",
            "{ $addFields: { trangThaiMaMuc: '$nguoiNhanDauTien.TrangThaiLienThong.MaMuc' } }",
            "{ $group: { _id: '$_class', " +
                    "soLuongThanhCong: { $sum: { $cond: [ { $eq: ['$trangThaiMaMuc', 'Done'] }, 1, 0 ] } }, " +
                    "soLuongThatBai: { $sum: { $cond: [ { $eq: ['$trangThaiMaMuc', 'Fail'] }, 1, 0 ] } } " +
                    "} }",
            "{ $addFields: { " +
                    "tongSoBanGhi: { $add: ['$soLuongThanhCong', '$soLuongThatBai'] } " +
                    "} }"
    })
    ThongKeLienThongVanBan.TiLeVanBan thongKeTiLeVanBanNhan(int year);

    @Aggregation(pipeline = {
            "{ $match: { KieuLoaiGoiTin: 'sendEdoc', DinhDangGoiTin: 'edoc' } }",
            "{ $addFields: { year: { $year: { $toDate: { $multiply: ['$NoiGuiGoiTin.ThoiGianGui', 1] } } } } }",
            "{ $match: { year: ?0 } }",
            "{ $addFields: { nguoiNhanDauTien: { $arrayElemAt: ['$NoiNhanGoiTin', 0] } } }",
            "{ $addFields: { trangThaiMaMuc: '$nguoiNhanDauTien.TrangThaiLienThong.MaMuc' } }",
            "{ $group: { _id: '$_class', " +
                    "soLuongThanhCong: { $sum: { $cond: [ { $eq: ['$trangThaiMaMuc', 'Done'] }, 1, 0 ] } }, " +
                    "soLuongThatBai: { $sum: { $cond: [ { $eq: ['$trangThaiMaMuc', 'Fail'] }, 1, 0 ] } } " +
                    "} }",
            "{ $addFields: { " +
                    "tongSoBanGhi: { $add: ['$soLuongThanhCong', '$soLuongThatBai'] } " +
                    "} }"
    })
    ThongKeLienThongVanBan.TiLeVanBan thongKeTiLeVanBanGui(int year);
}
