package com.example.integration.connection;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

@Getter
@Setter
public class SpringLectureRedisConnectionBase {

    public String host;
    public int port;

    public LettuceConnectionFactory connFactory;

    public SpringLectureRedisConnectionBase(String host, int port) {
        this.host = host;
        this.port = port;
    }

    public LettuceConnectionFactory getConnectionFactory() {
        connFactory = new LettuceConnectionFactory();

        return connFactory;
    }

    public void distroy() {
        if (connFactory != null) {
            connFactory.destroy();
        }

    }

    public void reset() {
        if (connFactory != null) {
            connFactory.resetConnection();
        }
    }

}
