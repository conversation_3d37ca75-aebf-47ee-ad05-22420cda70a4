package com.example.integration.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
@Configuration
public class GlobalEnvProperty {

    public Environment env;

    public Map<String, Object> grpcProps;
    public Map<String, Object> distributedProps;

    public GlobalEnvProperty(Environment env) {

        this.env = env;
        grpcProps = new HashMap<>();
        distributedProps = new HashMap<>();
        if (env instanceof ConfigurableEnvironment) {
            for (PropertySource<?> propertySource : ((ConfigurableEnvironment) env).getPropertySources()) {
                if (propertySource instanceof EnumerablePropertySource) {
                    for (String key : ((EnumerablePropertySource) propertySource).getPropertyNames()) {
                        if (key.contains("flexcore.lgspmgt.integrated.grpc.")) {
                            grpcProps.put(key, propertySource.getProperty(key));
                        } else if (key.contains("flexcore.lgspmgt.distributed.")) {
                            distributedProps.put(key, propertySource.getProperty(key));
                        } else if (key.contains("other keys here...")) {

                        }
                    }
                }
            }
        }

    }

    private String getPropValue() {
        return null;
    }

    private void validateConfig() {
        //TODO validate required properties
    }
}
