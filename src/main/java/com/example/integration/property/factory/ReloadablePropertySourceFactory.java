//package com.example.integration.property.factory;
//
//import org.springframework.core.env.PropertySource;
//import org.springframework.core.io.FileSystemResource;
//import org.springframework.core.io.FileUrlResource;
//import org.springframework.core.io.Resource;
//import org.springframework.core.io.UrlResource;
//import org.springframework.core.io.support.DefaultPropertySourceFactory;
//import org.springframework.core.io.support.EncodedResource;
//
//import java.io.IOException;
//
//public class ReloadablePropertySourceFactory extends DefaultPropertySourceFactory {
//
//    @Override
//    public PropertySource<?> createPropertySource(String name, EncodedResource encodedResource) throws IOException {
//
//        Resource internal = encodedResource.getResource();
//
//        if (internal instanceof FileSystemResource) {
//
//            return new ReloadablePropertySource(name, ((FileSystemResource) internal).getPath());
//        }
//        if (internal instanceof FileUrlResource) {
//
//            return super.createPropertySource(name, encodedResource);
//        }
//        if (internal instanceof UrlResource) {
//
//            return super.createPropertySource(name, encodedResource);
//
//            //return new ReloadablePropertySource(name, ((UrlResource) internal).getURL());
//        }
//
//        return super.createPropertySource(name, encodedResource);
//    }
//
//}
