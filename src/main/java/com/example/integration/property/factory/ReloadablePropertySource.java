//package com.example.integration.property.factory;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.configuration.PropertiesConfiguration;
//import org.apache.commons.configuration.reloading.FileChangedReloadingStrategy;
//import org.springframework.core.env.PropertySource;
//import org.springframework.util.ObjectUtils;
//
//import java.net.URL;
//
//@Slf4j
//public class ReloadablePropertySource<T> extends PropertySource<T> {
//
//    private static final String _DEFAULT_NAME = "flexconfig";
//    PropertiesConfiguration propertiesConfiguration;
//
//    public ReloadablePropertySource(String name, PropertiesConfiguration propertiesConfiguration) {
//        super(name);
//        this.propertiesConfiguration = propertiesConfiguration;
//    }
//
//    public ReloadablePropertySource(String name, String path) {
//
//        super(ObjectUtils.isEmpty(name) ? _DEFAULT_NAME : name);
//
//        try {
//            this.propertiesConfiguration = new PropertiesConfiguration(path);
//            this.propertiesConfiguration.setReloadingStrategy(new FileChangedReloadingStrategy());
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//    }
//
//    public ReloadablePropertySource(String name, URL url) {
//
//        super(ObjectUtils.isEmpty(name) ? _DEFAULT_NAME : name);
//
//        try {
//            this.propertiesConfiguration = new PropertiesConfiguration(url);
//            propertiesConfiguration.reload();
//
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//    }
//
//    @Override
//    public Object getProperty(String s) {
//        return propertiesConfiguration.getProperty(s);
//    }
//}
