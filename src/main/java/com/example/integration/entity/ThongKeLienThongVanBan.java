package com.example.integration.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThongKeLienThongVanBan {
    private List<MonthlySummary> khoiLuongVanBanNhan;
    private List<MonthlySummary> khoiLuongVanBanGui;
    private TiLeVanBan tiLeNhan = new TiLeVanBan();
    private TiLeVanBan tiLeGui = new TiLeVanBan();

    @Getter
    @Setter
    public static class TiLeVanBan{
        private Long soLuongThanhCong = 0L;
        private Long soLuongThatBai = 0L;
        private Long tongSoBanGhi = 0L;
    }
}
