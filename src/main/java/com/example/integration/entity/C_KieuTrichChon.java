package com.example.integration.entity;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseCategory;


import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(collection = DBConstant.C_KIEU_TRICH_CHON)
@JsonIgnoreProperties(ignoreUnknown = true)
public class C_KieuTrichChon extends BaseCategory<C_KieuTrichChon> {

	private static final long serialVersionUID = 1L;

	public C_KieuTrichChon() {
		super(true);
	}

	public C_KieuTrichChon(boolean isUpdate) {
		super(isUpdate);
	}

	@Override
	public String getType() {
		return DBConstant.C_KIEU_TRICH_CHON;
	}

	@Override
	public void setType(String type) {
		super.setType(DBConstant.C_KIEU_TRICH_CHON);
	}

	@Override
	public String getPrimKey() {

		String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

		super.setPrimKey(primKey);

		return primKey;
	}

	@Getter
	public enum TrangThai {
		REQ_HEADER_PROPERTY("01", "Thuộc tính yêu cầu"), REQ_QUERY_PARAMETTER("02", "Tham số truy vấn"),
		REQ_PATH_VALUE("03", "Giá trị tham số url"), REQ_BODY_RAWJSON("04", "Nội dung yêu cầu kiểu raw-json"),
		REQ_BODY_RAWXML("05", "Nội dung yêu cầu kiểu raw-xml"), REQ_BODY_RAWTXT("06", "Nội dung yêu cầu kiểu raw-text"),
		REQ_BODY_RAWHTML("07", "Nội dung yêu cầu kiểu raw-html"),
		REQ_BODY_RAWJAVASCRIPT("08", "Nội dung yêu cầu kiểu raw-javascript"),
		REQ_BODY_FORMDATA("09", "Nội dung yêu cầu kiêu formdata"),
		REQ_BODY_URLENDCODE("10", "Nội dung yêu cầ kiểu urlendcode"),
		REQ_BODY_BINARY("11", "Nội dung yêu cầu kiểu tệp dữ liệu"),
		REQ_BODY_GRAPHQL("12", "Nội dung yêu cầu kiểu graphql"), RESP_HEADER_PROPERTY("13", "Thuộc tính trả lời"),
		RESP_BODY_JSON("14", "Nội dung trả lời kiểu json"), RESP_BODY_XML("15", "Nội dung trả lời kiểu xml"),
		RESP_BODY_TXT("16", "Nội dung trả lời kiểu txt"), RESP_BODY_HTML("17", "Nội dung trả lời kiểu html"),
		RESP_BODY_BINARY("18", "Nội dung trả lời kiểu tệp dữ liệu");

		public String maMuc = StringPool.BLANK;
		public String tenMuc = StringPool.BLANK;

		TrangThai(String maMuc, String tenMuc) {
			setMaMuc(maMuc);
			setTenMuc(tenMuc);
		}

		public void setMaMuc(String maMuc) {
			this.maMuc = maMuc;
		}

		public void setTenMuc(String tenMuc) {
			this.tenMuc = tenMuc;
		}

		public static TrangThai fromMaMuc(String maMuc) {
			for (TrangThai trangThai : TrangThai.values()) {
				if (trangThai.maMuc.equals(maMuc)) {
					return trangThai;
				}
			}
			throw new IllegalArgumentException("enum not match: " + maMuc);
		}
	}
}
