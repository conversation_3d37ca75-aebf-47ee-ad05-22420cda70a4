package com.example.integration.entity;

import java.util.ArrayList;
import java.util.List;

import com.example.integration.constant.DBConstant;
import com.example.integration.entity.ext.CauHinhGoiTinExt;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseCategory;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(collection = DBConstant.C_TRUC_TICH_HOP)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TrucTichHop extends BaseCategory<TrucTichHop> {
	private static final long serialVersionUID = 1L;

	@JsonInclude
	@JsonProperty("DinhDangGoiTin")
	@Field(value = "DinhDangGoiTin", order = 3)
	public String dinhDangGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("CauHinhGoiTin")
	@Field(value = "CauHinhGoiTin", order = 3)
	public List<CauHinhGoiTinExt> cauHinhGoiTin = new ArrayList<>();

	@JsonInclude
	@JsonProperty("HeThongKetNoi")
	@Field(value = "HeThongKetNoi", order = 6)
	public List<HeThongKetNoiExt> heThongKetNoi = new ArrayList<>();


	@Override
	public String getType() {
		return DBConstant.C_TRUC_TICH_HOP;
	}

	@Override
	public void setType(String type) {
		super.setType(DBConstant.C_TRUC_TICH_HOP);
	}



	public TrucTichHop() {
		super(true);
	}

	public TrucTichHop(boolean isUpdate) {
		super(isUpdate);
	}

	@Override
	public String getPrimKey() {

		String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

		super.setPrimKey(primKey);

		return primKey;
	}

}
