package com.example.integration.entity;

import com.example.integration.base.entity.BaseCategory;
import com.example.integration.base.entity.BaseModel;
import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@Document(collection = DBConstant.T_LOG_DON_VI_LIEN_THONG)
@JsonIgnoreProperties(ignoreUnknown = true)
public class T_Log_DonViLienThong extends BaseModel<T_Log_DonViLienThong> {
    private static final long serialVersionUID = 1L;

    @JsonInclude
    @JsonProperty("TenMuc")
    @Field(value = "TenMuc", order = 8)
    public String tenMuc = StringPool.BLANK;
    @JsonInclude
    @JsonProperty("NguoiThucHien")
    @Field(value = "NguoiThucHien", order = 8)
    public String nguoiThucHien = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("Summary")
    @Field(value = "Summary", order = 8)
    public String summary = StringPool.BLANK;
    public T_Log_DonViLienThong(boolean isUpdate) {
        super(isUpdate);
    }


    @Override
    public String getType() {
        return DBConstant.T_LOG_DON_VI_LIEN_THONG;
    }

    @Override
    public void setType(String type) {
        super.setType(DBConstant.T_LOG_DON_VI_LIEN_THONG);
    }



    public T_Log_DonViLienThong() {
        super(false);
    }


    @Override
    public String getPrimKey() {

        String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

        super.setPrimKey(primKey);

        return primKey;
    }

}