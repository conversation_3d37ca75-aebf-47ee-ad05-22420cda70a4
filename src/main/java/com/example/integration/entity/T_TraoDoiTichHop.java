package com.example.integration.entity;

import java.util.ArrayList;
import java.util.List;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseModel;


import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(collection = DBConstant.T_TRAO_DOI_TICH_HOP)
@JsonIgnoreProperties(ignoreUnknown = true)
public class T_TraoDoiTichHop extends BaseModel<T_TraoDoiTichHop> {

	private static final long serialVersionUID = 1L;

	public T_TraoDoiTichHop() {
		super(true);

	}

	public T_TraoDoiTichHop(boolean isUpdate) {
		super(isUpdate);

	}

	@Override
	public String getType() {
		return DBConstant.T_TRAO_DOI_TICH_HOP;
	}

	@Override
	public void setType(String type) {
		super.setType(DBConstant.T_TRAO_DOI_TICH_HOP);
	}

	@JsonInclude
	@JsonProperty("MaDinhDanh")
	@Field(value = "MaDinhDanh", order = 5)
	public String maDinhDanh = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("DichVuTichHop")
	@Field(value = "DichVuTichHop", order = 6)
	public DichVuTichHop dichVuTichHop = new DichVuTichHop();

	@JsonInclude
	@JsonProperty("KetNoiID")
	@Field(value = "KetNoiID", order = 7)
	public String ketNoiID = StringPool.BLANK;
	
	@JsonInclude
	@JsonProperty("TenKetNoi")
	@Field(value = "TenKetNoi", order = 8)
	public String tenKetNoi = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("NoiDungYeuCau")
	@Field(value = "NoiDungYeuCau", order = 8)
	public TepDuLieu noiDungYeuCau;

	@JsonInclude
	@JsonProperty("NoiDungTraLoi")
	@Field(value = "NoiDungTraLoi", order = 8)
	public TepDuLieu noiDungTraLoi;

	@JsonInclude
	@JsonProperty("ThongSoKyThuat")
	@Field(value = "ThongSoKyThuat", order = 12)
	public List<ThongSoKyThuat> thongSoKyThuat = new ArrayList<>();
	
	@JsonInclude
	@JsonProperty("MaBanTin")
	@Field(value = "MaBanTin", order = 13)
	public String maBanTin;
	
	@JsonInclude
	@JsonProperty("BanTinTraoDoi")
	@Field(value = "BanTinTraoDoi", order = 14)
	public String banTinTraoDoi;

	@JsonInclude
	@JsonProperty("LoaiHoSo")
	@Field(value = "LoaiHoSo", order = 15)
	public String loaiHoSo;

	@Getter
	@Setter
	public static class DichVuTichHop {

		@JsonProperty("TenMuc")
		@Field(value = "TenMuc", order = 0)
		public String tenMuc = StringPool.BLANK;

		@JsonProperty("MaMuc")
		@Field(value = "MaMuc", order = 1)
		public String maMuc = StringPool.BLANK;
	}

	@Getter
	@Setter
	public static class ThongSoKyThuat {
		@JsonProperty("TenThongSo")
		@Field(value = "TenThongSo", order = 0)
		public String tenThongSo = StringPool.BLANK;

		@JsonProperty("NhanThongSo")
		@Field(value = "NhanThongSo", order = 1)
		public String nhanThongSo = StringPool.BLANK;

		@JsonProperty("GiaTriThongSo")
		@Field(value = "GiaTriThongSo", order = 2)
		public String giaTriThongSo = StringPool.BLANK;
	}

	@Override
	public String getPrimKey() {

		String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

		super.setPrimKey(primKey);

		return primKey;
	}

}
