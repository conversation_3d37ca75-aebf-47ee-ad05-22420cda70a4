package com.example.integration.entity;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseCategory;


import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(collection = DBConstant.C_GIAO_THUC_KET_NOI)
@JsonIgnoreProperties(ignoreUnknown = true)
public class C_GiaoThucKetNoi extends BaseCategory<C_GiaoThucKetNoi> {

	private static final long serialVersionUID = 1L;

	public C_GiaoThucKetNoi() {
		super(true);

	}
	public C_GiaoThucKetNoi(boolean isUpdate) {
		super(isUpdate);
	}

	@Override
	public String getType() {
		return DBConstant.C_GIAO_THUC_KET_NOI;
	}

	@Override
	public void setType(String type) {
		super.setType(DBConstant.C_GIAO_THUC_KET_NOI);
	}

	@Override
	public String getPrimKey() {

		String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

		super.setPrimKey(primKey);

		return primKey;
	}
	
	@Getter	
	public enum TrangThai {
		QLVB_CLIENT("01", "QLVB_CLIENT"),
		QLVB_SERVER("02", "QLVB_SERVER");

		public String maMuc = StringPool.BLANK;
		public String tenMuc = StringPool.BLANK;

		TrangThai(String maMuc, String tenMuc) {
			setMaMuc(maMuc);
			setTenMuc(tenMuc);
		}

		public void setMaMuc(String maMuc) {
			this.maMuc = maMuc;
		}

		public void setTenMuc(String tenMuc) {
			this.tenMuc = tenMuc;
		}
	}
}
