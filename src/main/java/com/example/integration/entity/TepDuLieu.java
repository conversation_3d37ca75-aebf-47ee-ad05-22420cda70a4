package com.example.integration.entity;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseModel;

import com.example.integration.entity.ext.LoaiNguonLuuTruExt;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Setter
@Getter
@Document(collection = DBConstant.T_TEP_DU_LIEU)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TepDu<PERSON><PERSON> extends BaseModel<TepDuLieu> {
    public static final long serialVersionUID = 1L;

    @JsonInclude
    @JsonProperty("MaDinhDanh")
    @Field(value = "MaDinhDanh", order = 4)
    public String maDinhDanh = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("TenTep")
    @Field(value = "TenTep", order = 5)
    public String tenTep = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("DinhDangTep")
    @Field(value = "DinhDangTep", order = 6)
    public String dinhDangTep = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("KichThuocTep")
    @Field(value = "KichThuocTep", order = 7)
    public Long kichThuocTep;

    @JsonInclude
    @JsonProperty("LoaiNguonLuuTru")
    @Field(value = "LoaiNguonLuuTru", order = 8)
    public LoaiNguonLuuTruExt loaiNguonLuuTru = new LoaiNguonLuuTruExt();

    @JsonInclude
    @JsonProperty("DuongDanURL")
    @Field(value = "DuongDanURL", order = 9)
    public String duongDanURL = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("MaHoaDuLieu")
    @Field(value = "MaHoaDuLieu", order = 10)
    public String maHoaDuLieu = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("MaTepDuLieu")
    @Field(value = "MaTepDuLieu", order = 11)
    public String maTepDuLieu = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("GhiChuVanBan")
    @Field(value = "GhiChuVanBan", order = 12)
    public String ghiChuVanBan = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("isTempFile")
    @Field(value = "isTempFile", order = 13)
    public boolean isTempFile = false;

    @JsonInclude
    @JsonProperty("hashFile")
    @Field(value = "hashFile", order = 13)
    public String hashFile = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("FileOriginal")
    @Field(value = "FileOriginal", order = 14)
    public String fileOriginal = StringPool.BLANK;

    @Transient
    @JsonInclude
    @JsonProperty("Ext")
    public String ext = StringPool.BLANK;

    @JsonProperty("Day")
    @Field(value = "Day", order = 14)
    public int day = 1;

    @JsonProperty("Month")
    @Field(value = "Month", order = 14)
    public int month = 1;

    @JsonProperty("Year")
    @Field(value = "Year", order = 14)
    public int year = 1900;

    @JsonProperty("DinhDangGoiTin")
    @Field(value = "DinhDangGoiTin", order = 15)
    public String dinhDangGoiTin = StringPool.BLANK;
    public TepDuLieu() {
        super(true);
        LocalDate today = LocalDate.now();
        this.day = today.getDayOfMonth();
        this.month = today.getMonthValue();
        this.year = today.getYear();
    }

    public TepDuLieu(boolean isUpdate) {
        super(isUpdate);
    }

    @Override
    public void setType(String type) {
        super.setType(DBConstant.T_TEP_DU_LIEU);
    }

    public String getPrimKey() {
        if (Validator.isNull(super.getPrimKey())) {
            String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;
            super.setPrimKey(primKey);
            return primKey;
        }
        return super.getPrimKey();
    }

    @Override
    public void set_id(ObjectId _id) {
        super.set_id(_id);
        String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;
        super.setPrimKey(primKey);
    }

}