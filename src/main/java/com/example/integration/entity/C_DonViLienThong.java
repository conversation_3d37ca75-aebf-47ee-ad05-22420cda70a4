package com.example.integration.entity;

import com.example.integration.base.entity.BaseCategory;
import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@Document(collection = DBConstant.C_DON_VI_LIEN_THONG)
@JsonIgnoreProperties(ignoreUnknown = true)
public class C_DonViLienThong extends BaseCategory<C_DonViLienThong> {
    private static final long serialVersionUID = 1L;

    @JsonInclude
    @JsonProperty("Mail")
    @Field(value = "Mail", order = 15)
    public String mail = StringPool.BLANK;
    @JsonInclude
    @JsonProperty("PID")
    @Field(value = "PID", order = 15)
    public String pid = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("ID_DVLT")
    @Field(value = "ID_DVLT", order = 15)
    public String ID_DVLT = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("Ma")
    @Field(value = "Ma", order = 15)
    public String ma = StringPool.BLANK;

    public C_DonViLienThong(boolean isUpdate) {
        super(isUpdate);
    }


    @Override
    public String getType() {
        return DBConstant.C_DON_VI_LIEN_THONG;
    }

    @Override
    public void setType(String type) {
        super.setType(DBConstant.C_DON_VI_LIEN_THONG);
    }



    public C_DonViLienThong() {
        super(false);
    }


    @Override
    public String getPrimKey() {

        String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

        super.setPrimKey(primKey);

        return primKey;
    }

}