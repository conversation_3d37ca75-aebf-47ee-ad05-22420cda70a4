package com.example.integration.entity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.example.integration.constant.DBConstant;
import com.example.integration.entity.ext.ThongSoKyThuatExt;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseModel;

import com.example.integration.entity.ext.HeThongKetNoiExt;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(collection = DBConstant.T_HANG_DOI_GOI_TIN)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HangDoiGoiTin extends BaseModel<HangDoiGoiTin> {

	private static final long serialVersionUID = 1L;

	public HangDoiGoiTin() {
		super(true);

	}

	public HangDoiGoiTin(boolean isUpdate) {
		super(isUpdate);

	}

	@Override
	public String getType() {
		return DBConstant.T_HANG_DOI_GOI_TIN;
	}

	@Override
	public void setType(String type) {
		super.setType(DBConstant.T_HANG_DOI_GOI_TIN);
	}

	@JsonInclude
	@JsonProperty("MaDinhDanh")
	@Field(value = "MaDinhDanh", order = 5)
	public String maDinhDanh = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("TrucTichHop")
	@Field(value = "TrucTichHop", order = 6)
	public TrucTichHop trucTichHop = new TrucTichHop();

	@JsonInclude
	@JsonProperty("MaGoiTin")
	@Field(value = "MaGoiTin", order = 7)
	public String maGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("DinhDangGoiTin")
	@Field(value = "DinhDangGoiTin", order = 8)
	public String dinhDangGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("KieuLoaiGoiTin")
	@Field(value = "KieuLoaiGoiTin", order = 9)
	public String kieuLoaiGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("TenKieuLoaiGoiTin")
	@Field(value = "TenKieuLoaiGoiTin", order = 10)
	public String tenKieuLoaiGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("ThongSoKyThuat")
	@Field(value = "ThongSoKyThuat", order = 12)
	public List<ThongSoKyThuatExt> thongSoKyThuat = new ArrayList<>();

	@JsonInclude
	@JsonProperty("NoiGuiGoiTin")
	@Field(value = "NoiGuiGoiTin", order = 11)
	public HeThongKetNoiExt noiGuiGoiTin = new HeThongKetNoiExt();

	@JsonInclude
	@JsonProperty("NoiNhanGoiTin")
	@Field(value = "NoiNhanGoiTin", order = 12)
	public List<HeThongKetNoiExt> noiNhanGoiTin = new ArrayList<>();

	@JsonInclude
	@JsonProperty("NoiDungGoiTin")
	@Field(value = "NoiDungGoiTin", order = 13)
	public TepDuLieu noiDungGoiTin = new TepDuLieu();

	@JsonIgnore
	@Field(value = "Headers", order = 13)
	public Map<String, String> headers = new HashMap<>();

	@JsonProperty("NoiDungTraVe")
	@Field(value = "NoiDungTraVe", order = 14)
	public String noiDungTraVe = StringPool.BLANK;;

	@JsonInclude
	@JsonProperty("ContentEdoc")
	@Field(value = "ContentEdoc", order = 15)
	public ContentEdoc contentEdoc = new ContentEdoc();

	@Setter
	@Getter
	public static class ContentEdoc {

		public String loaiGoiTin = StringPool.BLANK;
		public String soKyHieu = StringPool.BLANK;
		public String tieuDe = StringPool.BLANK;
		public String kieuVanBan = StringPool.BLANK;
		public String trangThaiVanBan = StringPool.BLANK;
		public String daNhan = "1";
	}

	@Override
	public String getPrimKey() {

		String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

		super.setPrimKey(primKey);

		return primKey;
	}

}
