package com.example.integration.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThongKeEdoc {
    private List<MonthlySummary> edocByMonth = new ArrayList<>();
    private List<MonthlySummary> hosoByMonth = new ArrayList<>();
    private HardDisk hardDisk = new HardDisk();

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HardDisk {
        String totalSpace  = "50G";
        String metaSize = "100MB";
        double percent = 0.2;


    }
}
