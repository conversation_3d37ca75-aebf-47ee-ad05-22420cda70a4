package com.example.integration.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class InfoMail {

    @JsonProperty("To")
    public String to = StringPool.BLANK;

    @JsonProperty("Subject")
    public String subject = StringPool.BLANK;

    @JsonProperty("Body")
    public String body = StringPool.BLANK;
}
