package com.example.integration.entity.ext;

import com.example.integration.constant.DBConstant;
import com.example.integration.entity.C_KieuTrichChon;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import org.springframework.data.mongodb.core.mapping.Field;

public class ThongSoKyThuatExt {
    @JsonProperty("@type")
    @Field(value = DBConstant.TYPE, order = 0)
    public String type = DBConstant.S_THONG_SO_KY_THUAT;

    @JsonProperty("TenThongSo")
    @Field(value = "TenThongSo", order = 1)
    public String tenThongSo = StringPool.BLANK;

    @JsonProperty("NhanThongSo")
    @Field(value = "NhanThongSo", order = 2)
    public String nhanThongSo = StringPool.BLANK;

    @JsonProperty("GiaTriThongSo")
    @Field(value = "GiaTriThongSo", order = 3)
    public String giaTriThongSo = StringPool.BLANK;

    @JsonProperty("KieuTrichChon")
    @Field(value = "KieuTrichChon", order = 3)
    public C_KieuTrichChon kieuTrichChon;
}
