package com.example.integration.entity.ext;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CauHinhGoiTinExt {
    @JsonProperty("@type")
    @Field(value = DBConstant.TYPE, order = 0)
    public String type = DBConstant.S_CAU_HINH_GOI_TIN;

    @JsonProperty("KieuLoaiGoiTin")
    @Field(value = "KieuLoaiGoiTin", order = 1)
    public String kieuLoaiGoiTin = StringPool.BLANK;

    @JsonProperty("TenKieuLoaiGoiTin")
    @Field(value = "TenKieuLoaiGoiTin", order = 2)
    public String tenKieuLoaiGoiTin = StringPool.BLANK;

    @JsonProperty("ThongSoKyThuat")
    @Field(value = "ThongSoKyThuat", order = 2)
    public List<ThongSoKyThuatExt> thongSoKyThuat = new ArrayList<>();


}
