package com.example.integration.entity.ext;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;


import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HeThongKetNoiExt {

	@JsonProperty("MaKetNoi")
	@Field(value = "MaKetNoi", order = 1)
	public String maKetNoi = StringPool.BLANK;

	@JsonProperty("TenKetNoi")
	@Field(value = "TenKetNoi", order = 2)
	public String tenKetNoi = StringPool.BLANK;

	@JsonProperty("GiaoThucKetNoi")
	@Field(value = "GiaoThucKetNoi", order = 3)
	public GiaoThucKetNoi giaoThucKetNoi = new GiaoThucKetNoi();

	@JsonProperty("CauHinhKetNoi")
	@Field(value = "CauHinhKetNoi", order = 4)
	public String cauHinhKetNoi = StringPool.BLANK;

	@JsonProperty("ChuoiMauNoiNhan")
	@Field(value = "ChuoiMauNoiNhan", order = 5)
	public String chuoiMauNoiNhan = StringPool.BLANK;

	@JsonProperty("TanSuatQuet")
	@Field(value = "TanSuatQuet", order = 6)
	public long tanSuatQuet = 0;

	@JsonProperty("ThoiGianHieuLuc")
	@Field(value = "ThoiGianHieuLuc", order = 7)
	public long thoiGianHieuLuc = 0;

	@JsonProperty("ThoiGianKetNoi")
	@Field(value = "ThoiGianKetNoi", order = 8)
	public long thoiGianKetNoi = 0;

	@JsonProperty("ThoiGianGui")
	@Field(value = "ThoiGianGui", order = 9)
	public long thoiGianGui = 0;

	@JsonProperty("TimeVdxpCreate")
	@Field(value = "TimeVdxpCreate", order = 10)
	public String timeVdxpCreate = "";

	@JsonProperty("TimeVdxpUpdate")
	@Field(value = "TimeVdxpUpdate", order = 11)
	public String timeVdxpUpdate = "";

	@JsonProperty("TrangThaiLienThong")
	@Field(value = "TrangThaiLienThong", order = 12)
	public TrangThaiLienThongExt trangThaiLienThong = new TrangThaiLienThongExt();

	@JsonProperty("Email")
	@Field(value = "Email", order = 13)
	public String email = "";

	@JsonProperty("Telegram")
	@Field(value = "Telegram", order = 14)
	public String telegram = "";

	@JsonProperty("TeleNotification")
	@Field(value = "TeleNotification", order = 15)
	public TeleNotification teleNotification = new TeleNotification();

	@JsonProperty("EmailNotification")
	@Field(value = "EmailNotification", order = 16)
	public EmailNotification emailNotification = new EmailNotification();

	@Getter
	@Setter
	public static class GiaoThucKetNoi {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.C_GIAO_THUC_KET_NOI;

		@JsonProperty("MaMuc")
		@Field(value = "MaMuc", order = 1)
		public String maMuc = StringPool.BLANK;

		@JsonProperty("TenMuc")
		@Field(value = "TenMuc", order = 2)
		public String tenMuc = StringPool.BLANK;
	}

	@Getter
	@Setter
	public static class TeleNotification {
		@JsonProperty("isShowVanBanTonTime")
		@Field(value = "isShowVanBanTonTime", order = 1)
		public boolean isShowVanBanTonTime = true;

		@JsonProperty("isShowVanBanTonUnit")
		@Field(value = "isShowVanBanTonUnit", order = 2)
		public boolean isShowVanBanTonUnit = true;

		@JsonProperty("isShowVanBanLoiTime")
		@Field(value = "isShowVanBanLoiTime", order = 3)
		public boolean isShowVanBanLoiTime = true;

		@JsonProperty("isShowVanBanLoiUnit")
		@Field(value = "isShowVanBanLoiUnit", order = 4)
		public boolean isShowVanBanLoiUnit = true;

		@JsonProperty("isShowVanBanLateTime")
		@Field(value = "isShowVanBanLateTime", order = 5)
		public boolean isShowVanBanLateTime = true;

		@JsonProperty("isShowVanBanLateUnit")
		@Field(value = "isShowVanBanLateUnit", order = 6)
		public boolean isShowVanBanLateUnit = true;

		@JsonProperty("isShowVanBanLateType")
		@Field(value = "isShowVanBanLateType", order = 7)
		public boolean isShowVanBanLateType = true;

		@JsonProperty("isShowVanBanOneHourTime")
		@Field(value = "isShowVanBanOneHourTime", order = 8)
		public boolean isShowVanBanOneHourTime = true;

		@JsonProperty("isShowVanBanOneHourUnit")
		@Field(value = "isShowVanBanOneHourUnit", order = 9)
		public boolean isShowVanBanOneHourUnit = true;

		@JsonProperty("isShowVanBanOneHourType")
		@Field(value = "isShowVanBanOneHourType", order = 10)
		public boolean isShowVanBanOneHourType = true;

		@JsonProperty("isShowVanHanhTrucTime")
		@Field(value = "isShowVanHanhTrucTime", order = 11)
		public boolean isShowVanHanhTrucTime = true;

		@JsonProperty("isShowVanHanhTrucUnit")
		@Field(value = "isShowVanHanhTrucUnit", order = 12)
		public boolean isShowVanHanhTrucUnit = true;

		@JsonProperty("isShowVanHanhTrucType")
		@Field(value = "isShowVanHanhTrucType", order = 13)
		public boolean isShowVanHanhTrucType = true;
	}

	@Getter
	@Setter
	public static class EmailNotification {
		@JsonProperty("isShowVanBanTonTime")
		@Field(value = "isShowVanBanTonTime", order = 1)
		public boolean isShowVanBanTonTime = true;

		@JsonProperty("isShowVanBanTonUnit")
		@Field(value = "isShowVanBanTonUnit", order = 2)
		public boolean isShowVanBanTonUnit = true;

		@JsonProperty("isShowVanBanLoiTime")
		@Field(value = "isShowVanBanLoiTime", order = 3)
		public boolean isShowVanBanLoiTime = true;

		@JsonProperty("isShowVanBanLoiUnit")
		@Field(value = "isShowVanBanLoiUnit", order = 4)
		public boolean isShowVanBanLoiUnit = true;

		@JsonProperty("isShowVanBanLateTime")
		@Field(value = "isShowVanBanLateTime", order = 5)
		public boolean isShowVanBanLateTime = true;

		@JsonProperty("isShowVanBanLateUnit")
		@Field(value = "isShowVanBanLateUnit", order = 6)
		public boolean isShowVanBanLateUnit = true;

		@JsonProperty("isShowVanBanLateType")
		@Field(value = "isShowVanBanLateType", order = 7)
		public boolean isShowVanBanLateType = true;

		@JsonProperty("isShowVanBanOneHourTime")
		@Field(value = "isShowVanBanOneHourTime", order = 8)
		public boolean isShowVanBanOneHourTime = true;

		@JsonProperty("isShowVanBanOneHourUnit")
		@Field(value = "isShowVanBanOneHourUnit", order = 9)
		public boolean isShowVanBanOneHourUnit = true;

		@JsonProperty("isShowVanBanOneHourType")
		@Field(value = "isShowVanBanOneHourType", order = 10)
		public boolean isShowVanBanOneHourType = true;

		@JsonProperty("isShowVanHanhTrucTime")
		@Field(value = "isShowVanHanhTrucTime", order = 11)
		public boolean isShowVanHanhTrucTime = true;

		@JsonProperty("isShowVanHanhTrucUnit")
		@Field(value = "isShowVanHanhTrucUnit", order = 12)
		public boolean isShowVanHanhTrucUnit = true;

		@JsonProperty("isShowVanHanhTrucType")
		@Field(value = "isShowVanHanhTrucType", order = 13)
		public boolean isShowVanHanhTrucType = true;
	}
}
