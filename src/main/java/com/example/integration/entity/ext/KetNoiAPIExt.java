package com.example.integration.entity.ext;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;

import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class KetNoiAPIExt {
	
	@JsonProperty("KetNoiID")
	@Field(value = "KetNoiID", order = 1)
	public String ketNoiID = StringPool.BLANK;

	@JsonProperty("TenKetNoi")
	@Field(value = "TenKetNoi", order = 2)
	public String tenKetNoi = StringPool.BLANK;

	@JsonProperty("ThongSoKyThuat")
	@Field(value = "ThongSoKyThuat", order = 6)
	public List<ThongSoKyThuatExt> thongSoKyThuat = new ArrayList<>();
}
