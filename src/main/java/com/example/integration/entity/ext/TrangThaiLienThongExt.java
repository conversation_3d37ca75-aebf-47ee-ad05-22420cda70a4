package com.example.integration.entity.ext;

import com.example.integration.constant.Constant;
import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@NoArgsConstructor
public class TrangThaiLienThongExt {
    @JsonProperty("@type")
    @Field(value = DBConstant.TYPE, order = 0)
    public String type = DBConstant.C_TRANG_THAI_LIEN_THONG;

    @JsonProperty("MaMuc")
    @Field(value = "MaMuc", order = 1)
    public String maMuc = Constant.STATUS_INITIAL;

    @JsonProperty("TenMuc")
    @Field(value = "TenMuc", order = 2)
    public String tenMuc = "Khởi tạo";

    public TrangThaiLienThongExt(String maMuc) throws Exception {

        this.setMaMuc(maMuc);
        switch (maMuc) {
            case Constant.STATUS_INITIAL:
                this.setTenMuc("Khởi tạo");
                break;
            case Constant.STATUS_PROCESSING:
                this.setTenMuc("Đang xử lý");
                break;
            case Constant.STATUS_DONE:
                this.setTenMuc("Hoàn thành");
                break;
            case Constant.STATUS_FAIL:
                this.setTenMuc("Thất bại");
                break;
            default:
                throw new Exception("No ma trang thai mapping");
        }
    }
}
