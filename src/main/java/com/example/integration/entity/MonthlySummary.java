package com.example.integration.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MonthlySummary {

    private Long totalSize;

//    @JsonIgnore
    private Long totalCount;

    @JsonProperty("month")
    private int _id;

    private String formattedSize;
}
