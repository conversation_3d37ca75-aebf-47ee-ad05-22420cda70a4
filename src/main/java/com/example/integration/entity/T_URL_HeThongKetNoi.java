package com.example.integration.entity;

import com.example.integration.base.entity.BaseCategory;
import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@Document(collection = DBConstant.T_URL_HE_THONG_KET_NOI)
@JsonIgnoreProperties(ignoreUnknown = true)
public class T_URL_HeThongKetNoi extends BaseCategory<T_URL_HeThongKetNoi> {
    private static final long serialVersionUID = 1L;


    @JsonInclude
    @JsonProperty("URL")
    @Field(value = "URL", order = 6)
    public String URL = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("MaKetNoi")
    @Field(value = "MaKetNoi", order = 6)
    public String maKetNoi = StringPool.BLANK;

    public T_URL_HeThongKetNoi(boolean isUpdate) {
        super(isUpdate);
    }


    @Override
    public String getType() {
        return DBConstant.T_URL_HE_THONG_KET_NOI;
    }

    @Override
    public void setType(String type) {
        super.setType(DBConstant.T_URL_HE_THONG_KET_NOI);
    }



    public T_URL_HeThongKetNoi() {
        super(true);
    }


    @Override
    public String getPrimKey() {

        String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

        super.setPrimKey(primKey);

        return primKey;
    }

}
