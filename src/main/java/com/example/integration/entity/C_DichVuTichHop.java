package com.example.integration.entity;

import java.util.ArrayList;
import java.util.List;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import com.example.integration.base.entity.BaseCategory;

import com.example.integration.entity.ext.KetNoiAPIExt;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(collection = DBConstant.C_DICH_VU_TICH_HOP)
@JsonIgnoreProperties(ignoreUnknown = true)
public class C_DichVuTichHop extends BaseCategory<C_DichVuTichHop> {
	
	private static final long serialVersionUID = 1L;

	@JsonInclude
	@JsonProperty("KetNoiAPI")
	@Field(value = "KetNoiAPI", order = 6)
	public List<KetNoiAPIExt> ketNoiAPI = new ArrayList<>();


	public C_DichVuTichHop() {
		super(true);
	}

	public C_DichVuTichHop(boolean isUpdate) {
		super(isUpdate);
	}

	@Override
	public String getType() {
		return DBConstant.C_DICH_VU_TICH_HOP;
	}

	@Override
	public void setType(String type) {
		super.setType(DBConstant.C_DICH_VU_TICH_HOP);
	}

	@Override
	public String getPrimKey() {

		String primKey = this.get_id() != null ? this.get_id().toHexString() : StringPool.BLANK;

		super.setPrimKey(primKey);

		return primKey;
	}

}
