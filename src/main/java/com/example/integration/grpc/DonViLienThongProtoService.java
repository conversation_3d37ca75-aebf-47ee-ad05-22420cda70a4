package com.example.integration.grpc;

import com.example.integration.base.entity.TrangThaiDuLieu;
import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.hub.service.DonViLienThongService;
import com.example.integration.repository.DonViLienThongRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.grpc.DonViLienThongGrpc;
import com.fds.flex.grpc.DonViLienThongOuterClass;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.util.Optional;

@Slf4j
@GrpcService
public class DonViLienThongProtoService extends DonViLienThongGrpc.DonViLienThongImplBase {
    @Autowired
    private DonViLienThongRepository donViLienThongRepository;

    @Autowired
    private DonViLienThongService donViLienThongService;

    public void findByMaDinhDanh(DonViLienThongOuterClass.FindByMDDRequest request,
                                 StreamObserver<DonViLienThongOuterClass.APIResponse> responseObserver) {
        String maDinhDanh = request.getMaDinhDanh();
        String ten = request.getTen();
        DonViLienThongOuterClass.APIResponse.Builder response = DonViLienThongOuterClass.APIResponse.newBuilder();

        Optional<C_DonViLienThong> donViLienThongOpt = donViLienThongService.findByMaMuc(maDinhDanh, TrangThaiDuLieu.TrangThai.ChinhThuc.getMaMuc());

        if (!donViLienThongOpt.isPresent()) {
            C_DonViLienThong donViLienThong = new C_DonViLienThong();
            donViLienThong.setMaMuc(maDinhDanh);
            donViLienThong.setThoiGianTao(Instant.now().toEpochMilli());
            donViLienThong.setTenMuc(ten);
            donViLienThongRepository.save(donViLienThong);
            response.setStatus(201)
                    .setMessage("DVLT đã được thêm thành công")
                    .setReq(maDinhDanh)
                    .setResp("DVLT đã được thêm")
                    .build();

        } else {
            C_DonViLienThong donViLienThong = donViLienThongOpt.get();
            ObjectMapper mapper = new ObjectMapper();
            String content = null;
            try {
                content = mapper.writeValueAsString(donViLienThong);
            } catch (JsonProcessingException e) {
                System.out.println(e.getMessage());
            }
            response.setReq(maDinhDanh)
                    .setResp(content)
                    .setStatus(200)
                    .setMessage("DVLT đã tồn tại");
        }

        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }
}
