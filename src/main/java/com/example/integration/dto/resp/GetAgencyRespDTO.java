package com.example.integration.dto.resp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetAgencyRespDTO {

    @Getter
    @Setter
    @AllArgsConstructor
    public static class DataObject {

        @JsonProperty("code")
        public String code = StringPool.BLANK;

        @JsonProperty("name")
        public String name = StringPool.BLANK;

        @JsonProperty("centerCode")
        public String centerCode = StringPool.BLANK;

        @JsonProperty("pid")
        public String pid = StringPool.BLANK;

        @JsonProperty("id")
        public String id = StringPool.BLANK;
    }


    @JsonProperty("data")
    public List<DataObject> data = new ArrayList<>();

    @JsonProperty("ErrorDesc")
    public String errorDesc = StringPool.BLANK;

    @JsonProperty("ErrorCode")
    public String errorCode = StringPool.BLANK;

    @JsonProperty("status")
    public String status = StringPool.BLANK;
}
