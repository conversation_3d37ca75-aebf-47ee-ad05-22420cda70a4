package com.example.integration.dto.resp;


import com.example.integration.dto.req.HangDoiGoiTinReqDTO;
import com.example.integration.entity.HangDoiGoiTin;

public class HangDoiGoiTinRespDTO extends BaseRespDTO<HangDoiGoiTinReqDTO, HangDoiGoiTin> {
	public HangDoiGoiTinRespDTO(String errorCode, String messageCode, String message, HangDoiGoiTinReqDTO requestData,
                                HangDoiGoiTin responseData) {
		super(errorCode, messageCode, message, requestData, responseData);
	}
}
