package com.example.integration.dto.resp;

/**
 * @param <T>
 * @param <E>
 * <AUTHOR>
 */
public class BaseRespDTO<T, E> {

	public String errorCode;

	public String messageCode;

	public String message;

	public long systemTime;

	public T req;

	public E resp;

	public BaseRespDTO(String errorCode, String messageCode, String message, T req, E resp) {
		this.errorCode = errorCode;
		this.messageCode = messageCode;
		this.message = message;
		this.systemTime = System.currentTimeMillis();
		this.req = req;
		this.resp = resp;
	}

	public BaseRespDTO(String message, T req, E resp) {

		this.message = message;
		this.systemTime = System.currentTimeMillis();
		this.req = req;
		this.resp = resp;
	}
}
