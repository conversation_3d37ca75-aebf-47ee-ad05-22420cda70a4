package com.example.integration.dto.resp;

import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.entity.C_DonViLienThong;

public class DonViLienThongResqDTO extends BaseRespDTO<DonViLienThongReqDTO, C_DonViLienThong> {
    public DonViLienThongResqDTO(String errorCode, String messageCode, String message, DonViLienThongReqDTO requestData,
                              C_DonViLienThong responseData) {
        super(errorCode, messageCode, message, requestData, responseData);
    }
}
