package com.example.integration.dto.resp;

import com.example.integration.dto.req.LogDonViLienThongReqDTO;
import com.example.integration.entity.T_Log_DonViLienThong;

public class LogDonViLienThongResqDTO extends BaseRespDTO<LogDonViLienThongReqDTO, T_Log_DonViLienThong> {
    public LogDonViLienThongResqDTO(String errorCode, String messageCode, String message, LogDonViLienThongReqDTO requestData,
                                    T_Log_DonViLienThong responseData) {
        super(errorCode, messageCode, message, requestData, responseData);
    }
}
