package com.example.integration.dto.req;

import com.example.integration.entity.TepDuLieu;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class HangDoiGoiTinReqDTO {

	@JsonInclude
	@JsonProperty("TrucTichHop")
	public HangDoiGoiTinDTO_TrucTichHop trucTichHop = new HangDoiGoiTinDTO_TrucTichHop();

	@JsonInclude
	@JsonProperty("MaGoiTin")
	public String maGoiTin = StringPool.BLANK; //DocId

	@JsonInclude
	@JsonProperty("DinhDangGoiTin")
	public String dinhDangGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("KieuLoaiGoiTin")
	public String kieuLoaiGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("TenKieuLoaiGoiTin")
	public String tenKieuLoaiGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("NoiGuiGoiTin")
	public HangDoiGoiTinDTO_HeThongKetNoi noiGuiGoiTin = new HangDoiGoiTinDTO_HeThongKetNoi();

	@JsonInclude
	@JsonProperty("NoiNhanGoiTin")
	public List<HangDoiGoiTinDTO_HeThongKetNoi> noiNhanGoiTin = new ArrayList<>();

	@JsonInclude
	@JsonProperty("NoiDungGoiTin")
	public HangDoiGoiTinDTO_TepDuLieu tepDuLieu = new HangDoiGoiTinDTO_TepDuLieu();

	@JsonProperty("Headers")
	public Map<String, String> headers = new HashMap<>();

	@Setter
	@Getter
	public static class HangDoiGoiTinDTO_HeThongKetNoi {

		@JsonInclude
		@JsonProperty("MaKetNoi")
		public String maKetNoi = StringPool.BLANK;

		@JsonInclude
		@JsonProperty("TenKetNoi")
		public String tenKetNoi = StringPool.BLANK;

		@JsonInclude
		@JsonProperty("ThoiGianGui")
		public long thoiGianGui = System.currentTimeMillis();
	}

	@Setter
	@Getter
	public static class HangDoiGoiTinDTO_TrucTichHop {

		@JsonInclude
		@JsonProperty("MaMuc")
		public String maMuc = StringPool.BLANK;
	}

	@Setter
	@Getter
	public static class HangDoiGoiTinDTO_TepDuLieu {

		@JsonInclude
		@JsonProperty("MaDinhDanh")
		public String maDinhDanh = StringPool.BLANK;

	}

}
