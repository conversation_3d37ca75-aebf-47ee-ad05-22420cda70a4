package com.example.integration.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DonViLienThongReqDTO {

    @JsonInclude
    @JsonProperty("MaMuc")
    public String maMuc = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("TenMuc")
    public String tenMuc = StringPool.BLANK;
}
