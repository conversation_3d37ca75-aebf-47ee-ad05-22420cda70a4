package com.example.integration.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class HangDoiGoiTinTrangThaiReqDTO {

	@JsonInclude
	@JsonProperty("TrangThai")
	public String trangThai;

	@JsonInclude
	@JsonProperty("NoiNhanGoiTin")
	public HangDoiGoiTinTrangThaiReqDTO_HeThongKetNoi noiNhanGoiTin = new HangDoiGoiTinTrangThaiReqDTO_HeThongKetNoi();


	@Setter
	@Getter
	public static class HangDoiGoiTinTrangThaiReqDTO_HeThongKetNoi {

		@JsonInclude
		@JsonProperty("<PERSON>KetN<PERSON>")
		public String maKetNoi = StringPool.BLANK;
	}

}
