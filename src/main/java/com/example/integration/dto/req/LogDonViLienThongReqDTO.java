package com.example.integration.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogDonViLienThongReqDTO {

    @JsonInclude
    @JsonProperty("MaMuc")
    public String maMuc = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("TenMuc")
    public String tenMuc = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("NguoiThucHien")
    @Field(value = "NguoiThucHien", order = 8)
    public String nguoiThucHien = StringPool.BLANK;

}
