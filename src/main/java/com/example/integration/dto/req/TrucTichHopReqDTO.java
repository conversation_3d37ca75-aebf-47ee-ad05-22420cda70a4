package com.example.integration.dto.req;

import com.example.integration.entity.ext.CauHinhGoiTinExt;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TrucTichHopReqDTO {

	@JsonInclude
	@JsonProperty("MaMuc")
	public String maMuc = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("TenMuc")
	public String tenMuc = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("DinhDangGoiTin")
	public String dinhDangGoiTin = StringPool.BLANK;

	@JsonInclude
	@JsonProperty("CauHinhGoiTin")
	@Field(value = "CauHinhGoiTin", order = 3)
	public List<CauHinhGoiTinExt> cauHinhGoiTin = new ArrayList<>();

	@JsonInclude
	@JsonProperty("HeThongKetNoi")
	public List<HeThongKetNoiExt> heThongKetNoi = new ArrayList<>();
}
