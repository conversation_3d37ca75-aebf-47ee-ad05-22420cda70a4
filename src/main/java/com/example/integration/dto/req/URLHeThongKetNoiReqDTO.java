package com.example.integration.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class URLHeThongKetNoiReqDTO {

    @JsonInclude
    @JsonProperty("MaMuc")
    public String maMuc = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("TenMuc")
    public String tenMuc = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("URL")
    @Field(value = "URL", order = 6)
    public String URL = StringPool.BLANK;

    @JsonInclude
    @JsonProperty("<PERSON>K<PERSON>N<PERSON>")
    @Field(value = "MaKetN<PERSON>", order = 6)
    public String maKetNoi = StringPool.BLANK;
}
