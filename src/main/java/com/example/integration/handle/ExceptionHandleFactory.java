package com.example.integration.handle;

import com.fds.flex.common.constant.MarkerConstant;
import com.fds.flex.common.dto.resp.ErrorModel;
import com.fds.flex.common.dto.resp.ExceptionRespDTO;
import com.fds.flex.common.exception.*;
import com.fds.flex.common.utility.string.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MarkerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.beans.factory.annotation.Value;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@ControllerAdvice
public class ExceptionHandleFactory {

    @Value("${spring.servlet.multipart.max-request-size}")
    private String maxRequestSize;

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Object> handleMaxSizeException(MaxUploadSizeExceededException ex) {
        String message = "Kích thước file vượt quá giới hạn cho phép (" + maxRequestSize + ")";
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(
                new ExceptionRespDTO(HttpStatus.PAYLOAD_TOO_LARGE.value(), HttpStatus.PAYLOAD_TOO_LARGE.name(),null,
                        message, null)
        );
    }

    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(BadRequestException ex) {
        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new ExceptionRespDTO(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.name(), ex.errors,
                        ex.message, ex.trace));

    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(MethodArgumentNotValidException ex) {

        // JSONObject a = new JSONObject();

        List<ErrorModel> errors = new ArrayList<>();

        ex.getFieldErrors();
        ex.getFieldErrors().forEach(e -> {
            ErrorModel error = new ErrorModel();
            error.setCode(e.getDefaultMessage());
            error.setField(e.getField());
            error.setMessage(e.getDefaultMessage());

            errors.add(error);
        });

        String trace = StringPool.BLANK;// ex.getMessage()

        String message =
                "Validation failed for object='" + ex.getObjectName() + "'. Error count: " + ex.getErrorCount();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new ExceptionRespDTO(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.name(), errors,
                        message, trace));

    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(UnauthorizedException ex) {
        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new ExceptionRespDTO(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.name(), ex.errors,
                        ex.message, ex.trace));

    }

    @ExceptionHandler(ForbiddenException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(ForbiddenException ex) {
        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(new ExceptionRespDTO(HttpStatus.FORBIDDEN.value(), HttpStatus.FORBIDDEN.name(), ex.errors,
                        ex.message, ex.trace));

    }

    @ExceptionHandler(NotfoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(NotfoundException ex) {

        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new ExceptionRespDTO(HttpStatus.NOT_FOUND.value(), HttpStatus.NOT_FOUND.name(), ex.errors,
                        ex.message, ex.trace));

    }

    @ExceptionHandler(ConflictException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(ConflictException ex) {
        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(new ExceptionRespDTO(HttpStatus.CONFLICT.value(), HttpStatus.CONFLICT.name(), ex.errors,
                        ex.message, ex.trace));

    }

    @ExceptionHandler(InternalServerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(InternalServerException ex) {
        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ExceptionRespDTO(HttpStatus.INTERNAL_SERVER_ERROR.value(),
                        HttpStatus.INTERNAL_SERVER_ERROR.name(), ex.errors, ex.message, ex.trace));

    }

    @ExceptionHandler(ServiceUnavailableException.class)
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    @ResponseBody
    public ResponseEntity<?> exceptionHandle(ServiceUnavailableException ex) {
        log.error(MarkerFactory.getMarker(MarkerConstant.ERROR), ex.getMessage(), "SYSTEM", ex.getError());
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(new ExceptionRespDTO(HttpStatus.SERVICE_UNAVAILABLE.value(),
                        HttpStatus.SERVICE_UNAVAILABLE.name(), ex.errors, ex.message, ex.trace));

    }
}
