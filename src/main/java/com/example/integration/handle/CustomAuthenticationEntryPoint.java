package com.example.integration.handle;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.common.constant.ContentTypes;
import com.fds.flex.common.dto.resp.ExceptionRespDTO;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        ExceptionRespDTO exceptionRespDTO = new ExceptionRespDTO(HttpStatus.UNAUTHORIZED.value(),
                HttpStatus.UNAUTHORIZED.name(), null, authException.getMessage(), authException.getMessage());

        response.setContentType(ContentTypes.APPLICATION_JSON);

        response.setStatus(HttpStatus.UNAUTHORIZED.value());

        OutputStream out = response.getOutputStream();

        ObjectMapper mapper = new ObjectMapper();

        mapper.writeValue(out, exceptionRespDTO);

        out.flush();

        out.close();
    }

}
