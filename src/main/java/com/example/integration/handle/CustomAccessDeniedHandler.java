package com.example.integration.handle;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.common.constant.ContentTypes;
import com.fds.flex.common.dto.resp.ExceptionRespDTO;
import com.fds.flex.common.ultility.MessageUtil;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

@Component
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    public static final String LANGUAGE = "language";
    public static final String USER_HAS_NOT_PERMISSION_TO_DO_THIS_ACTION = "User.Permission.not_action";

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       AccessDeniedException accessDeniedException) throws IOException {

        ExceptionRespDTO exceptionRespDTO = new ExceptionRespDTO(HttpStatus.FORBIDDEN.value(),
                HttpStatus.FORBIDDEN.name(), null, MessageUtil.getVietNameseMessageFromResourceBundle(LANGUAGE,
                USER_HAS_NOT_PERMISSION_TO_DO_THIS_ACTION), accessDeniedException.getMessage());

        response.setContentType(ContentTypes.APPLICATION_JSON);

        response.setStatus(HttpStatus.FORBIDDEN.value());

        OutputStream out = response.getOutputStream();

        ObjectMapper mapper = new ObjectMapper();

        mapper.writeValue(out, exceptionRespDTO);

        out.flush();

        out.close();
    }

}
