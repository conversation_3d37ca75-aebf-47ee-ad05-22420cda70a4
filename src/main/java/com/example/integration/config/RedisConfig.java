package com.example.integration.config;

import com.example.integration.connection.SpringLectureRedisStandaloneConnection;
import com.example.integration.constant.DBConstant;
import com.example.integration.listener.EnvChangeEventListener;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.GenericToStringSerializer;


@Configuration
@Slf4j
@DependsOn("applicationProperty")
public class RedisConfig {

    @Autowired
    private EnvChangeEventListener envChangeEventListener;

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        String host = GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_DISTRIBUTED_CACHE_REDIS_HOST));
        int port = GetterUtil.getInteger(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_DISTRIBUTED_CACHE_REDIS_PORT));
        String password = GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_DISTRIBUTED_CACHE_REDIS_PASSWORD));
        if (Validator.isNotNull(host) && port > 0) {
            LettuceConnectionFactory lettuceConnectionFactory = new SpringLectureRedisStandaloneConnection(host, port).getConnectionFactory();
            if (password != null && !password.isEmpty()) {
                lettuceConnectionFactory.setPassword(password);
            }
            return lettuceConnectionFactory;
        }
        return new LettuceConnectionFactory();
    }

    @Bean
    public MessageListenerAdapter messageListenerAdapter() {
        return new MessageListenerAdapter(envChangeEventListener);
    }

    // Disabled Redis message listener container to prevent connection errors
     @Bean
     public RedisMessageListenerContainer container(RedisConnectionFactory redisConnectionFactory,
                                                   MessageListenerAdapter messageListenerAdapter) {
         RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
         redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
         ChannelTopic systemTopic = new ChannelTopic(DBConstant.C_THAM_SO_CAU_HINH);
         redisMessageListenerContainer.addMessageListener(messageListenerAdapter, systemTopic);
         return redisMessageListenerContainer;
     }

    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory());
        redisTemplate.setValueSerializer(new GenericToStringSerializer<>(Object.class));
        return redisTemplate;
    }
}
