package com.example.integration.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "flexcore.integration.securityconfig")
public class JWTIssuersConfig {
    private List<String> issuers;
    private List<String> antMatchers;
    private List<String> requestFilter;
    private List<String> corsDomain = Collections.singletonList("*");
    private boolean allowCredentials = false;
}