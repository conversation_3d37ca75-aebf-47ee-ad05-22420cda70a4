//package com.example.integration.config;
//
//
//import com.example.integration.property.factory.ReloadablePropertySourceFactory;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.PropertySource;
//
//@Configuration
//@PropertySource(value = "${integration.hub.distributed.property.filePath}", factory = ReloadablePropertySourceFactory.class, name = "dbconfig")
//public class LoadProperties {
//
//}