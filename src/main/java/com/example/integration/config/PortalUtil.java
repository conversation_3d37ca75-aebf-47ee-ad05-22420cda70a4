package com.example.integration.config;

import com.example.integration.constant.Constant;
import com.example.integration.entity.HangDoiGoiTin;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.Base64Variants;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.vpcp.services.utils.FileUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.*;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import java.security.MessageDigest;

@Setter
@Getter
@Slf4j
public class PortalUtil {

	public final static String _CONTEXT_PATH_PATTEN = "(^/)[a-z0-9-/_]+";


	public static String timestampToStringDate(long timestamp) {
		try {
			LocalDateTime dateTime = Instant.ofEpochMilli(timestamp)
					.atZone(ZoneId.of("Asia/Ho_Chi_Minh"))
					.toLocalDateTime();

			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
			return dateTime.format(formatter);
		} catch (Exception e) {
			return "";
		}
	}

	public static JsonNode parseTxtFileUsingStream (String txtFilePath) {
		try {
			//Logic: đầu tiên đọc "data" từ file gốc .txt, sau đó decode và lưu các trường edxml cần thiết vào 1 thư mục temp,
			// sau đó parse ra JsonNode từ file mới đó
//			String folderTempEdxml = GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_TEMP_EDXML_DIR));
//
//			if(!FileUtil.exist(folderTempEdxml)) {
//				FileUtil.mkdirs(folderTempEdxml);
//			}
//
//			// Lấy tên file edxml
//			String fileName = txtFilePath.substring(txtFilePath.lastIndexOf('/') + 1);
//			String docId = fileName.substring(0, fileName.lastIndexOf('.'));
//
//			String edxmlPath = folderTempEdxml +"/" + docId + ".edxml";

			//Đọc json file, xử lý khi gặp key "data"
			JsonFactory f = new JsonFactory();
			try (InputStream in = Files.newInputStream(Paths.get(txtFilePath));
                 JsonParser jp = f.createParser(in)
			) {
				while (jp.nextToken() != null) {
					if (jp.currentToken() == JsonToken.FIELD_NAME && "data".equals(jp.getCurrentName())) {
                        jp.nextToken();
						InputStream base64Stream = new ByteArrayInputStream(jp.getBinaryValue());
						return createXmlMetadata(base64Stream);
                    }
                }

			}  catch (IOException e) {
				log.error("Error when parseTxtFileUsingStream " + txtFilePath + ": " + e.getMessage());
				e.printStackTrace();
			}
		} catch (Exception e) {
			log.error("Error when parse txt file using stream: " + e.getMessage());
			e.printStackTrace();
		}
		return null;
	}

	public static JsonNode parseEdxmlFileUsingStream (String filePath)  {
		try {
			//Tạo Metadata của file gửi đi, file gửi đi đã sẵn định dạng EDXML
			InputStream inputStream = Files.newInputStream(Paths.get(filePath));
			return createXmlMetadata(inputStream);
		} catch (Exception e) {
			log.error("Error when parseEdxmlFileUsingStream: " + e.getMessage());
			e.printStackTrace();
		}

		return null;
	}

	private static JsonNode createXmlMetadata(InputStream inputStream) {
		XMLInputFactory factory = XMLInputFactory.newInstance();
		try {
			XMLStreamReader reader = factory.createXMLStreamReader(inputStream);

			StringWriter writer = new StringWriter();
			boolean insideEnvelope = false;
			int depth = 0;

			while (reader.hasNext()) {
				int event = reader.next();

				if (event == XMLStreamConstants.START_ELEMENT &&
						reader.getLocalName().equalsIgnoreCase("edXMLEnvelope")) {
					insideEnvelope = true;
					depth = 1;
					writer.write("<" + reader.getLocalName());
					// Ghi thuộc tính nếu có
					for (int i = 0; i < reader.getAttributeCount(); i++) {
						writer.write(" " + reader.getAttributeLocalName(i) + "=\"" + reader.getAttributeValue(i) + "\"");
					}
					writer.write(">");
				} else if (insideEnvelope) {
					if (event == XMLStreamConstants.START_ELEMENT) {
						depth++;
						writer.write("<" + reader.getLocalName());
						for (int i = 0; i < reader.getAttributeCount(); i++) {
							writer.write(" " + reader.getAttributeLocalName(i) + "=\"" + reader.getAttributeValue(i) + "\"");
						}
						writer.write(">");
					} else if (event == XMLStreamConstants.CHARACTERS) {
						writer.write(reader.getText());
					} else if (event == XMLStreamConstants.END_ELEMENT) {
						writer.write("</" + reader.getLocalName() + ">");
						depth--;
						if (depth == 0) {
							break;
						}
					}
				}
			}

			reader.close();

			String envelopeXml = writer.toString();
			// Parse thành JsonNode
			XmlMapper xmlMapper = new XmlMapper();
			return xmlMapper.readTree(envelopeXml);

		} catch (Exception e) {
			log.error("Error when createXmlMetadata: " + e.getMessage());
			e.printStackTrace();
		}

		return null;
	}

	public static JsonNode parseEdxmlFile (String filePath) throws IOException {
		XmlMapper xmlMapper = new XmlMapper();
		return xmlMapper.readTree(new File(filePath));
	}

	public static String encodeFileToBase64(String filePath) throws IOException {
		File file = new File(filePath);
		if (!file.exists()) {
			throw new IOException("File không tồn tại: " + filePath);
		}
		
		byte[] fileBytes = Files.readAllBytes(file.toPath()); // Đọc file dưới dạng byte[]
		return Base64.getEncoder().encodeToString(fileBytes); // Mã hóa Base64
	}

	public static void encodeFileToBase64Stream(String filePath, OutputStream outputStream) throws IOException {
		File file = new File(filePath);
		if (!file.exists()) {
			throw new IOException("File không tồn tại: " + filePath);
		}

		try (
				InputStream fileInputStream = new FileInputStream(file);
				OutputStream base64OutputStream = Base64.getEncoder().wrap(outputStream)
		) {
			byte[] buffer = new byte[8192]; // Đọc từng phần 8KB
			int bytesRead;
			while ((bytesRead = fileInputStream.read(buffer)) != -1) {
				base64OutputStream.write(buffer, 0, bytesRead);
			}
		}
	}

	public static HangDoiGoiTin.ContentEdoc getEdxmlContent(String messageType, JsonNode root) {
		HangDoiGoiTin.ContentEdoc contentEdoc = new HangDoiGoiTin.ContentEdoc();
		try {
			JsonNode messageHeader = root
					.path("edXMLHeader")
					.path("MessageHeader");

			if(messageType.equalsIgnoreCase(Constant.MESSAGE_TYPE_EDOC)) {
				contentEdoc.setLoaiGoiTin("1|Văn bản điện tử");
				JsonNode code = messageHeader.path("Code");
				if(Validator.isNotNull(code)) {
					String codeNumber = code.has("CodeNumber") ? code.get("CodeNumber").asText() : "";
					String codeNotation = code.has("CodeNotation") ? code.get("CodeNotation").asText(): "";
					if(Validator.isNotNull(codeNumber) && Validator.isNotNull(codeNotation)) {
						contentEdoc.setSoKyHieu(codeNumber + "/" + codeNotation);
					}

					String tieuDe = messageHeader.has("Subject") ? messageHeader.get("Subject").asText() : "";
					if(Validator.isNotNull(tieuDe)) {
						contentEdoc.setTieuDe(tieuDe);
					}

					String statusCode = messageHeader.has("StatusCode") ? messageHeader.get("StatusCode").asText() : "";
					if(Validator.isNotNull(statusCode)) {
						contentEdoc.setTrangThaiVanBan(statusCode);
					}
				}

				JsonNode traceHeaderList = root
						.path("edXMLHeader")
						.path("TraceHeaderList");

				JsonNode business = traceHeaderList.path("Bussiness");
				if (Validator.isNotNull(business)) {
					String businessDocType = business.has("BussinessDocType") ? business.get("BussinessDocType").asText() : "";
					String bussinessDocReason = business.has("BussinessDocReason") ? business.get("BussinessDocReason").asText() : "";
					if (Validator.isNotNull(businessDocType)) {
						contentEdoc.setKieuVanBan(businessDocType + "|" + bussinessDocReason);
					}
				}

				return contentEdoc;
			} else if (messageType.equalsIgnoreCase(Constant.MESSAGE_TYPE_STATUS)) {
				contentEdoc.setLoaiGoiTin("2|Trạng thái văn bản");

				JsonNode responseFor = messageHeader.path("ResponseFor");

				String soKiHieu = responseFor.has("Code") ? responseFor.get("Code").asText(): "";
				contentEdoc.setSoKyHieu(Validator.isNotNull(soKiHieu) ? soKiHieu : "");
				contentEdoc.setTrangThaiVanBan(getTrangThaiVanBan(messageHeader));

				return contentEdoc;
			}
		} catch (Exception e) {
			log.error("Error when load edxmlFromTo", e);
		}
		return contentEdoc;
	}

	private static String getTrangThaiVanBan(JsonNode messageHeader) {
		String trangThaiVanBan = StringPool.BLANK;

		try {
			if(!messageHeader.has("StatusCode") || Validator.isNull(messageHeader.get("StatusCode"))) {
				log.warn("No statusCode was found");
				return trangThaiVanBan;
			}

			String statusCode = messageHeader.get("StatusCode").asText();
			if(statusCode.equalsIgnoreCase("01") || statusCode.equalsIgnoreCase("1")) {
				return statusCode + "|" + "Đã đến";
			} else if(statusCode.equalsIgnoreCase("02") || statusCode.equalsIgnoreCase("2")) {
				return statusCode + "|" + "Từ chối tiếp nhận";
			} else if(statusCode.equalsIgnoreCase("03") || statusCode.equalsIgnoreCase("3")) {
				return statusCode + "|" + "Đã tiếp nhận";
			} else if(statusCode.equalsIgnoreCase("04")|| statusCode.equalsIgnoreCase("4")) {
				return statusCode + "|" + "Phân công";
			} else if(statusCode.equalsIgnoreCase("05")|| statusCode.equalsIgnoreCase("5")) {
				return statusCode + "|" + "Đang xử lý";
			} else if(statusCode.equalsIgnoreCase("06")|| statusCode.equalsIgnoreCase("6")) {
				return statusCode + "|" + "Hoàn thành";
			} else if(statusCode.equalsIgnoreCase("13")) {
				return statusCode + "|" + "Lấy lại";
			} else if(statusCode.equalsIgnoreCase("15")) {
				return statusCode + "|" + "Đồng ý lấy lại/cập nhật văn bản";
			} else if(statusCode.equalsIgnoreCase("16")) {
				return statusCode + "|" + "Từ chối lấy lại/cập nhật văn bản";
			}

			log.error("No status code was matching with: " + statusCode);

		} catch (Exception e) {
			log.error("Error when get trang thai van ban", e);
		}
		return trangThaiVanBan;
	}

	public static List<JsonNode> getEdxmlFromTo(String messageType, JsonNode root) throws Exception {
		List<JsonNode> result = new ArrayList<>();

		if(messageType.equalsIgnoreCase(Constant.MESSAGE_TYPE_EDOC)) {

			JsonNode from = root
					.path("edXMLHeader")
					.path("MessageHeader")
					.path("From");

			result.add(Validator.isNull(from) ? null : from);

			JsonNode to = root
					.path("edXMLHeader")
					.path("MessageHeader")
					.path("To");
			result.add(Validator.isNull(to) ? null : to);

			return result;
		} else if (messageType.equalsIgnoreCase(Constant.MESSAGE_TYPE_STATUS)) {

			JsonNode from = root
					.path("edXMLHeader")
					.path("MessageHeader")
					.path("From");

			if(!from.has("OrganId")) {
				from = root
						.path("edXMLHeader")
						.path("Status")
						.path("From");
			}

			result.add(Validator.isNull(from) ? null : from);

			JsonNode to = root
					.path("edXMLHeader")
					.path("MessageHeader")
					.path("ResponseFor");

			if(!to.has("OrganId")) {
				to = root
						.path("edXMLHeader")
						.path("Status")
						.path("ResponseFor");
			}

			result.add(Validator.isNull(to) ? null : to);


			return result;

		}
		return null;
	}


	public static String getContextPathFromRequestUri(String requestUri) {

		Pattern pattern = Pattern.compile(_CONTEXT_PATH_PATTEN, Pattern.CASE_INSENSITIVE);

		final Matcher matcher = pattern.matcher(requestUri);

		if (matcher.find()) {
			return removeEndSlash(matcher.group());
		}

		return removeEndSlash(requestUri);
	}

	public static String createSoapRequest(String maTichHop, String soDinhDanh, String hoVaTen,
										   String ngayThangNamSinh, String maCanBo, String maYeuCau) {
		return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
				"<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
				"xmlns:dan=\"http://dancuquocgia.bca\">\n" +
				"    <soapenv:Header/>\n" +
				"    <soapenv:Body>\n" +
				"        <dan:TraCuuThongTinCongDan xmlns:dan=\"http://dancuquocgia.bca\">\n" +
				"            <dan:MaYeuCau>" + maYeuCau + "</dan:MaYeuCau>\n" +
				"            <dan:MaDVC></dan:MaDVC>\n" +
				"            <dan:MaTichHop>" + maTichHop + "</dan:MaTichHop>\n" +
				"            <dan:MaCanBo>" + maCanBo + "</dan:MaCanBo>\n" +
				"            <dan:SoDinhDanh>" + soDinhDanh + "</dan:SoDinhDanh>\n" +
				"            <dan:HoVaTen>" + hoVaTen + "</dan:HoVaTen>\n" +
				"            <dan:NgayThangNamSinh>\n" +
				"                <dan:NgayThangNam>" + ngayThangNamSinh + "</dan:NgayThangNam>\n" +
				"            </dan:NgayThangNamSinh>\n" +
				"        </dan:TraCuuThongTinCongDan>\n" +
				"    </soapenv:Body>\n" +
				"</soapenv:Envelope>";
	}

	public static String toHex256(String stringBeforeHash) {
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        digest.update(stringBeforeHash.getBytes(StandardCharsets.UTF_8));
		byte[] digest1 = digest.digest();
		return String.format("%064x", new BigInteger(1, digest1));
	}

	public static String getCurrentTimeForLGSP() {
		try {
			LocalDateTime now = LocalDateTime.now();

			// Định dạng thời gian
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyyy'T'HHmmss+SSS");

			// Chuyển sang định dạng yêu cầu
			return now.format(formatter);
		} catch (Exception e) {
			log.error("Error when get current time for LGSP: ", e);
		}
		return "";
	}


	public static List<String> remakeStaticResourceDir(List<String> dirs) {
		dirs.forEach(dir -> {
			int index = dirs.indexOf(dir);
			dir = remakeStaticResourceDir(dir);
			dirs.set(index, dir);
		});

		return dirs;
	}

	public static String remakeStaticResourceDir(String dir) {
		if (dir.endsWith(StringPool.SLASH)) {
			return dir;
		}

		return dir.concat(StringPool.SLASH);
	}

	public static String removeEndSlash(String s) {

		if (!s.equals("/") && s.lastIndexOf("/") == s.length() - 1) {
			s = s.substring(0, s.length() - 1);
		}

		return s;
	}


	public static String convertMapToJson(Map<String, Object> map) {
		String json = StringPool.BLANK;
		Map<String, Object> nestedMap = new HashMap<>();
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			String[] parts = entry.getKey().split("\\.");
			Map<String, Object> currentMap = nestedMap;
			for (int i = 0; i < parts.length - 1; i++) {
				currentMap = (Map<String, Object>) currentMap.computeIfAbsent(parts[i], k -> new HashMap<>());
			}
			currentMap.put(parts[parts.length - 1], entry.getValue());
		}

		try {
			ObjectMapper mapper = new ObjectMapper();
			json = mapper.writeValueAsString(nestedMap);

		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage());
		}

		return json;
	}

}
