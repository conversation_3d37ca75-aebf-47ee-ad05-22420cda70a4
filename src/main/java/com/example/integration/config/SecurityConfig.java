package com.example.integration.config;

import com.example.integration.filter.CustomPostAuthenticationFilter;
import com.example.integration.handle.CustomAccessDeniedHandler;
import com.example.integration.handle.CustomAuthenticationEntryPoint;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.fds.flex.common.utility.string.StringUtil;

import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.proc.DefaultJOSEObjectTypeVerifier;
import com.nimbusds.jose.shaded.json.JSONArray;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.security.oauth2.server.resource.authentication.JwtIssuerAuthenticationManagerResolver;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationFilter;

import java.util.*;
import java.util.stream.Collectors;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
@Slf4j
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Getter
    Map<String, AuthenticationManager> authenticationManagers = new HashMap<>();
    @Getter
    Map<String, JwtDecoder> jwtDecoderManagers = new HashMap<>();

    @Getter
    JwtIssuerAuthenticationManagerResolver authenticationManagerResolver = new JwtIssuerAuthenticationManagerResolver(
            authenticationManagers::get);

    @Autowired
    private CustomPostAuthenticationFilter customPostAuthenticationFilter;

    @Autowired
    private JWTIssuersConfig jwtIssuersConfig;

    protected void configure(HttpSecurity http) throws Exception {

        List<String> propsIssuers = jwtIssuersConfig.getIssuers();

        propsIssuers.forEach(issuer -> addManager(authenticationManagers, issuer));

        http.cors().and().csrf().disable().authorizeRequests()
                .antMatchers("/**").permitAll().and()
                .authorizeRequests(authorizeRequests -> authorizeRequests.anyRequest().authenticated())
                .oauth2ResourceServer(oauth2ResourceServer -> oauth2ResourceServer.authenticationManagerResolver(this.authenticationManagerResolver)
                        .accessDeniedHandler(accessDeniedHandler())
                        .authenticationEntryPoint(authenticationEntryPoint()))
                .addFilterAfter(customPostAuthenticationFilter, BearerTokenAuthenticationFilter.class);

    }

    public void addManager(Map<String, AuthenticationManager> authenticationManagers, String issuer) {
        JwtDecoder jwtDecoder;
        String typeSSO = GetterUtil.get(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_SSO_SYSTEM),
                StringPool.BLANK);
        if (Validator.isNotNull(typeSSO) && issuer.contains(typeSSO)) {

            jwtDecoder = NimbusJwtDecoder.withJwkSetUri(  issuer + "/.well-known/openid-configuration/jwks")
                    .jwtProcessorCustomizer(customizer -> {
                        customizer.setJWSTypeVerifier(new DefaultJOSEObjectTypeVerifier<>(new JOSEObjectType("at+jwt")));
                    })
                    .build();


            log.info("configure security jwt decoder with issuer: {}", issuer);

            final JwtAuthenticationConverter jwtAuthenticationConverter = new JwtAuthenticationConverter();

            jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(new MyJwtAuthenticationConverter());

            JwtAuthenticationProvider authenticationProvider = new JwtAuthenticationProvider(jwtDecoder);

            authenticationProvider.setJwtAuthenticationConverter(jwtAuthenticationConverter);

            authenticationManagers.put(issuer, authenticationProvider::authenticate);

            jwtDecoderManagers.put(issuer, jwtDecoder);

            return;
        }
        try {
            jwtDecoder = JwtDecoders.fromIssuerLocation(issuer);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            try {
                jwtDecoder = NimbusJwtDecoder.withJwkSetUri(issuer + "/protocol/openid-connect/certs")
                        .jwsAlgorithm(SignatureAlgorithm.RS256).build();
            } catch (Exception ex2) {
                log.error(ex2.getMessage());
                return;
            }
        }

        log.info("configure security jwt decoder with issuer: {}", issuer);

        final JwtAuthenticationConverter jwtAuthenticationConverter = new JwtAuthenticationConverter();
        jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(new MyJwtAuthenticationConverter());

        JwtAuthenticationProvider authenticationProvider = new JwtAuthenticationProvider(jwtDecoder);
        authenticationProvider.setJwtAuthenticationConverter(jwtAuthenticationConverter);
        authenticationManagers.put(issuer, authenticationProvider::authenticate);
        jwtDecoderManagers.put(issuer, jwtDecoder);



    }

    @Bean
    CustomAccessDeniedHandler accessDeniedHandler() {
        return new CustomAccessDeniedHandler();
    }

    @Bean
    CustomAuthenticationEntryPoint authenticationEntryPoint() {
        return new CustomAuthenticationEntryPoint();
    }

    public static class MyJwtAuthenticationConverter implements Converter<Jwt, Collection<GrantedAuthority>> {
        public Collection<GrantedAuthority> convert(final Jwt jwt) {
            String typeSSO = GetterUtil.get(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_SSO_SYSTEM),
                    StringPool.BLANK);
            String issuer = jwt.getClaims().get("iss") != null ? jwt.getClaims().get("iss").toString() : "";

            if (issuer.contains(typeSSO)) {

                String client_id = Optional.ofNullable((String) jwt.getClaims().get("idp")).orElse(null);


                final Map<String, Object> realmAccess = (Map<String, Object>) jwt.getClaims().get("resource_access");


                Object scopeClaim = jwt.getClaim("scope");

                List<String> scopes = new ArrayList<>();


                if (scopeClaim instanceof String) {

                    scopes = Arrays.asList(((String) scopeClaim).split(" "));
                } else if (scopeClaim instanceof JSONArray) {

                    for (int i = 0; i < ((JSONArray) scopeClaim).size(); i++) {
                        Object scopeObject = ((JSONArray) scopeClaim).get(i);
                        scopes.add(scopeObject.toString());
                    }
                }

                List<GrantedAuthority> grantedAuthorities = scopes.stream()
                        .map(scope -> "SCOPE_" + scope)
                        .map(SimpleGrantedAuthority::new)
                        .collect(Collectors.toList());

                if (Validator.isNotNull(realmAccess)) {

                    Map<String, Object> clientRolesMap = (Map<String, Object>) realmAccess.get("client_id");
                    if (clientRolesMap != null && clientRolesMap.containsKey("roles")) {
                        List<String> roles = (List<String>) clientRolesMap.get("roles");
                        if (roles != null && !roles.isEmpty()) {
                            grantedAuthorities.addAll(
                                    roles.stream()
                                            .map(roleName -> "ROLE_" + roleName)
                                            .map(SimpleGrantedAuthority::new)
                                            .collect(Collectors.toList())
                            );
                        }
                    }
                }
                return grantedAuthorities;
            }
            String client_id = (String) jwt.getClaims().get("azp");
            final Map<String, Object> realmAccess = (Map<String, Object>) jwt.getClaims().get("resource_access");

            String[] scopes = Validator.isNotNull((String) jwt.getClaim("scope")) ? StringUtil.split(
                    jwt.getClaim("scope"), StringPool.SPACE) : new String[0];

            List<GrantedAuthority> grantedAuthoritys = Arrays.stream(scopes).map(scope -> "SCOPE_" + scope)
                    .map(SimpleGrantedAuthority::new).collect(Collectors.toList());

            if (Validator.isNotNull(realmAccess)) {
                grantedAuthoritys.addAll(
                        ((List<String>) ((Map<String, Object>) realmAccess.get(client_id)).get("roles")).stream().map(roleName -> "ROLE_" + roleName)
                                .map(SimpleGrantedAuthority::new).collect(Collectors.toList()));
            }
            return grantedAuthoritys;
        }
    }
}
