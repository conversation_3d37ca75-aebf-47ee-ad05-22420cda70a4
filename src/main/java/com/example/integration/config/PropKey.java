package com.example.integration.config;

import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class PropKey {


    public static final String INTEGRATION_HUB_LGSP_HOSTLGSP = "integration.hub.lgsp.hostLgsp";
    public static final String INTEGRATION_HUB_LGSP_TOKEN_URL = "integration.hub.lgsp.tokenUrl";
    public static final String INTEGRATION_HUB_LGSP_GETRECEIVEDEDOCLIST_URL = "integration.hub.lgsp.getReceivedEdocListUrl";
    public static final String INTEGRATION_HUB_LGSP_SENDEDOC_URL = "integration.hub.lgsp.sendEdocUrl";
    public static final String INTEGRATION_HUB_LGSP_GETEDOC_URL = "integration.hub.lgsp.getEdocUrl";
    public static final String INTEGRATION_HUB_LGSP_UPDATESTATUS_URL = "integration.hub.lgsp.updateStatusUrl";
    public static final String INTEGRATION_HUB_LGSP_SYSTEMID = "integration.hub.lgsp.systemId";
    public static final String INTEGRATION_HUB_LGSP_SECRET_VDXP = "integration.hub.lgsp.secretVDXP";
    public static final String INTEGRATION_HUB_LGSP_VERSION = "integration.hub.lgsp.version";
    public static final String INTEGRATION_HUB_LGSP_SERVICEID = "integration.hub.lgsp.serviceId";
    public static final String INTEGRATION_HUB_LGSP_LGSPTO = "integration.hub.lgsp.lgspTo";
    public static final String INTEGRATION_HUB_LGSP_BASIC_USERNAME = "integration.hub.lgsp.basicUsername";
    public static final String INTEGRATION_HUB_LGSP_BASIC_PASSWORD = "integration.hub.lgsp.basicPassword";
    public static final String INTEGRATION_HUB_LGSP_USERNAME = "integration.hub.lgsp.username";
    public static final String INTEGRATION_HUB_LGSP_PASSWORD = "integration.hub.lgsp.password";

    public static final String TELEGRAM_BOT_TOKEN = "telegram.bot.token";

    public static final String INTEGRATION_HUB_SCHEDULER_GET_AGENCY_CRON = "integration.hub.scheduler.get_agency.cron";
    public static final String FLEXCORE_INTEGRATION_MAIL_HOST = "flexcore.integration.mail.host";
    public static final String FLEXCORE_INTEGRATION_MAIL_PORT     = "flexcore.integration.mail.port";
    public static final String FLEXCORE_INTEGRATION_MAIL_USERNAME = "flexcore.integration.mail.username";
    public static final String FLEXCORE_INTEGRATION_MAIL_PASSWORD = "flexcore.integration.mail.password";
    public static final String FLEXCORE_INTEGRATION_DATA_DIR = "flexcore.integration.data.dir";

    public static final String FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_DIR = "flexcore.portal.web.static-resource.dir";
    public static final String FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_SAVE_DIR = "flexcore.portal.web.static-resource-save.dir";
    public static final String FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_SDK_DIR = "flexcore.portal.web.static-resource-sdk.dir";
//    public static final String FLEXCORE_PORTAL_WEB_STATIC_TEMP_EDXML_DIR = "flexcore.portal.web.static-temp-edxml.dir";

    public static final String FLEXCORE_PORTAL_DISTRIBUTED_CACHE_REDIS_HOST = "integration.hub.distributed.cache.redis.host";
    public static final String FLEXCORE_PORTAL_DISTRIBUTED_CACHE_REDIS_PORT = "integration.hub.distributed.cache.redis.port";
    public static final String FLEXCORE_PORTAL_DISTRIBUTED_CACHE_REDIS_PASSWORD = "integration.hub.distributed.cache.redis.password";
    public static final String FLEXCORE_PORTAL_DISTRIBUTED_CACHE_PROPERTY_FILE_PATH = "integration.hub.distributed.property.filePath";
    public static final String FLEXCORE_MANAGEMENT_JSON_DVLT = "abed.plat";
    public static final String FLEXCORE_PORTAL_SSO_SYSTEM = "flexcore.integration.sso.system";
    public static final String FLEXCORE_INTEGRATION_SSO_URL = "flexcore.integration.sso.url";
    @Getter
    public static Map<String, Object> keyMap;

    public static void setKeyMap(Map<String, Object> keyMap) {
        PropKey.keyMap = keyMap;
    }

}
