package com.example.integration.controller;

import com.example.integration.dto.req.TrucTichHopReqDTO;
import com.example.integration.dto.resp.BaseRespDTO;
import com.example.integration.dto.resp.TrucTichHopRespDTO;
import com.example.integration.entity.TrucTichHop;
import com.example.integration.hub.action.TrucTichHopAction;
import com.fds.flex.common.ultility.ResponseUtil;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/tructichhop")
@Slf4j
@Validated
public class TrucTichHopController {
	@Autowired
	TrucTichHopAction action;

	@PostMapping(value = "", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE }, produces = {
			MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> add(@RequestBody TrucTichHopReqDTO request) {

		log.debug("Get body create TrucTichHop: {} ", new JSONObject(request));

		TrucTichHop object = action.add(request);

		return ResponseEntity.ok(new TrucTichHopRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
				request, object));

	}

	@PostMapping(value = "/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE,
			MediaType.TEXT_PLAIN_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> update(@PathVariable(name = "id") String id,
			@RequestBody TrucTichHopReqDTO request) {

		log.debug("Get body update TrucTichHop: {} ", new JSONObject(request));

		TrucTichHop object = action.update(id, request);

		return ResponseEntity.ok(new TrucTichHopRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
				request, object));

	}

	@GetMapping(value = "/{id}")
	public ResponseEntity<?> findById(@PathVariable(name = "id") String id) {

		log.debug("Find TrucTichHop: {} ", id);

		TrucTichHop TrucTichHop = action.findById(id);

		return ResponseEntity.ok(new TrucTichHopRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
				null, TrucTichHop));

	}

	@DeleteMapping(value = "/{id}")
	public ResponseEntity<?> delete(@PathVariable(name = "id") String id) {

		log.debug("Delete TrucTichHop: {} ", id);

		action.delete(id);
		
		return ResponseEntity.ok(new BaseRespDTO<String, String>(ResponseUtil.RespCode.SUCCESS.getCode(), "success",
				"success", id, "Delete success"));

	}

	@GetMapping(value = "/filter")
	public ResponseEntity<?> filter(@RequestParam(name = "keyword", required = false) String keyword,
			@RequestParam(name = "page") Integer page, @RequestParam(name = "size") Integer size,
									@RequestParam(name = "tuNgay", required = false) Long tuNgay,
									@RequestParam(name = "denNgay", required = false) Long denNgay,
			@RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
			@RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

		log.debug("Filter TrucTichHop: keyword: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}", keyword, page,
				size, orderFields, orderTypes);

		Page<TrucTichHop> result = action.filter(keyword, page, size,tuNgay,denNgay ,orderFields, orderTypes);

		return ResponseEntity.ok(result);

	}
}
