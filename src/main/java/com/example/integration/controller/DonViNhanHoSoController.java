package com.example.integration.controller;

import com.example.integration.config.PropKey;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.utility.string.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/donvinhanhoso")
@Slf4j
@Validated
public class DonViNhanHoSoController {

    private final String jsonFileName = "danhsachdonvinhanhoso.json";

    @GetMapping(value = "")
    public ResponseEntity<?> getDanhSach() {

        String dataDirLocation = GetterUtil.get(PropKey.getKeyMap().get(PropKey.FLEXCORE_INTEGRATION_DATA_DIR), System.getProperty("user.dir"));
        File dataDir = new File(dataDirLocation);
        if(!dataDir.exists()){
            dataDir.mkdir();
        }
        String fileDirLocation = dataDirLocation + StringPool.SLASH + jsonFileName;
        File jsonFile = new File(fileDirLocation);
        if(!jsonFile.exists()){
            return ResponseEntity.internalServerError().body("File not found: " + fileDirLocation);
        }

        try (FileInputStream fis = new FileInputStream(jsonFile)) {
            String result = StreamUtils.copyToString(fis, StandardCharsets.UTF_8);
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(result);
        } catch (IOException e) {
            return ResponseEntity
                    .internalServerError()
                    .body("Failed to read file: " + e.getMessage());
        }

    }

}

