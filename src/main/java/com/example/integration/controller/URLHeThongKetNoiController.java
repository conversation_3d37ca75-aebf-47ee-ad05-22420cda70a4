package com.example.integration.controller;

import com.example.integration.dto.req.URLHeThongKetNoiReqDTO;
import com.example.integration.dto.resp.BaseRespDTO;
import com.example.integration.dto.resp.URLHeThongKetNoiResqDTO;
import com.example.integration.entity.T_URL_HeThongKetNoi;
import com.example.integration.hub.action.URLHeThongKetNoiAction;
import com.fds.flex.common.ultility.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/urlhethongketnoi")
@Slf4j
@Validated
public class URLHeThongKetNoiController {
    @Autowired
    URLHeThongKetNoiAction action;

    @PostMapping(value = "", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
    public ResponseEntity<?> add(@RequestBody URLHeThongKetNoiReqDTO request) {

        log.debug("Get body create URLHeThongKetNoi: {} ", new JSONObject(request));

        T_URL_HeThongKetNoi object = action.add(request);

        return ResponseEntity.ok(new URLHeThongKetNoiResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    @PostMapping(value = "/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
    public ResponseEntity<?> update(@PathVariable(name = "id") String id,
                                    @RequestBody URLHeThongKetNoiReqDTO request) {

        log.debug("Get body update URLHeThongKetNoi: {} ", new JSONObject(request));

        T_URL_HeThongKetNoi object = action.update(id, request);

        return ResponseEntity.ok(new URLHeThongKetNoiResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<?> findById(@PathVariable(name = "id") String id) {

        log.debug("Find URLHeThongKetNoi: {} ", id);

        T_URL_HeThongKetNoi URLHeThongKetNoi = action.findById(id);

        return ResponseEntity.ok(new URLHeThongKetNoiResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                null, URLHeThongKetNoi));

    }

    @DeleteMapping(value = "/{id}")
    public ResponseEntity<?> delete(@PathVariable(name = "id") String id) {

        log.debug("Delete URLHeThongKetNoi: {} ", id);

        action.delete(id);

        return ResponseEntity.ok(new BaseRespDTO<String, String>(ResponseUtil.RespCode.SUCCESS.getCode(), "success",
                "success", id, "Delete success"));

    }

    @GetMapping(value = "/filter")
    public ResponseEntity<?> filter(@RequestParam(name = "keyword", required = false) String keyword,
                                    @RequestParam(name = "page") Integer page, @RequestParam(name = "size") Integer size,
                                    @RequestParam(name = "url", required = false) String url,
                                    @RequestParam(name = "tenMuc", required = false) String tenMuc,
                                    @RequestParam(name = "maKetNoi", required = false) String maKetNoi,
                                    @RequestParam(name = "tuNgay", required = false) Long tuNgay,
                                    @RequestParam(name = "denNgay", required = false) Long denNgay,
                                    @RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
                                    @RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

        log.debug("Filter TrucTichHop: keyword: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}", keyword, page,
                size, orderFields, orderTypes);

        Page<T_URL_HeThongKetNoi> result = action.filter(keyword, page, size,url,tenMuc,maKetNoi,tuNgay,denNgay,orderFields, orderTypes);

        return ResponseEntity.ok(result);

    }
}
