package com.example.integration.controller;

import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.hub.service.impl.SchedulerService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fds.flex.common.ultility.GetterUtil;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/XrdAdapter/adapter/")
@Slf4j
public class FakeController {
    @Autowired
    SchedulerService service;

    @GetMapping("/user")
    public ResponseEntity<?> getUserInfo() {
        JSONObject result = new JSONObject();
        result.put("name", "Tuan");
        result.put("age",10);
        result.put("address", "Ha Noi");
        return ResponseEntity.ok(result.toString());
    }

    @PostMapping("/testScheduler")
    public ResponseEntity<?> test() {
        long startTime = System.currentTimeMillis();
        JsonNode root = PortalUtil.parseTxtFileUsingStream("D:/P3/integration-hub/bin/save/2025/7/3e182826-af3d-4f57-aa59-60654f20f544.txt");
        long end = System.currentTimeMillis();
        log.info("Time: " + (end - startTime) + " ms");


        long startOld = System.currentTimeMillis();

        try {
            PortalUtil.parseEdxmlFile("D:/P3/integration-hub/bin/save/2025/7/guithufile50mb.edxml");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        long endOld = System.currentTimeMillis();
        log.info("Time Old: " + (endOld - startOld) + " ms");

        return ResponseEntity.ok("oke");
    }

    @PostMapping(value = "/security/oauth/token", produces = "application/json")
    public ResponseEntity<?> getToken(){
        JSONObject result = new JSONObject();
        result.put("access_token", "dfsldf-efsdf-dfefs");
        result.put("expires_in", 3600);
        return ResponseEntity.ok(result.toString());
    }

    @PostMapping(value = "bocContent", produces = "application/json",consumes = "application/json")
    public ResponseEntity<?> bocContent(){
        String uploadDir = PortalUtil.remakeStaticResourceDir(
                GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_DIR)));
        String filePath = uploadDir + "/b7878c53-1e8d-4b53-9018-65d39df48298.edxml";

        String fileContent = "";
//        try (FileInputStream inputStream = new FileInputStream(filePath)) {
//            StringBuilder content = new StringBuilder();
//            int byteRead;
//
//            while ((byteRead = inputStream.read()) != -1) {
//                content.append((char) byteRead);
//            }
//            fileContent = content.toString();
//            // Output the content
////            System.out.println(content.toString());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//
//        byte[] inputBytes = fileContent.getBytes();
//        String encodedString = Base64.getEncoder().encodeToString(inputBytes);
        long startOld = System.currentTimeMillis();

        try {
             PortalUtil.parseEdxmlFile(filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        long endOld = System.currentTimeMillis();
        log.info("Time Old: " + (endOld - startOld) + " ms");


        long startNew = System.currentTimeMillis();
        JsonNode root = PortalUtil.parseEdxmlFileUsingStream(filePath);

        long endNew = System.currentTimeMillis();
        log.info("Time New: " + (endNew - startNew) + " ms");

        JSONObject result = new JSONObject();
        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");

        return ResponseEntity.ok(result.toString());
    }

    @PostMapping(value = "getEdoc", produces = "application/json",consumes = "application/json")
    public ResponseEntity<?> getEdoc(){
        String uploadDir = PortalUtil.remakeStaticResourceDir(
                GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_DIR)));
        String filePath = uploadDir + "/b7878c53-1e8d-4b53-9018-65d39df48298.edxml";

        String fileContent = "";
        try (FileInputStream inputStream = new FileInputStream(filePath)) {
            StringBuilder content = new StringBuilder();
            int byteRead;

            while ((byteRead = inputStream.read()) != -1) {
                content.append((char) byteRead);
            }
            fileContent = content.toString();
            // Output the content
//            System.out.println(content.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }


        byte[] inputBytes = fileContent.getBytes();
        String encodedString = Base64.getEncoder().encodeToString(inputBytes);

        JSONObject result = new JSONObject();
        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");
        result.put("data", encodedString);

        return ResponseEntity.ok(result.toString());
    }

    @PostMapping(value = "/updateStatus", produces = "application/json",consumes = "application/json")
    public ResponseEntity<?> updateStatus(){

        JSONObject result = new JSONObject();
        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");

        return ResponseEntity.ok(result.toString());
    }

    @PostMapping(value = "/getReceivedEdocList", consumes = "application/json")
    public String getReceivedEdocList(){

        String result = "{\"data\":[{\"serviceType\":\"eDoc\",\"created_time\":" +
                "\"2018-09-18\",\"updated_time\":\"2018-09-25\"," +
                "\"messagetype\":\"eDoc\",\"status_desc\":\"fail\"," +
                "\"docId\":\"362d322a-8821-4029-a476-d4f55e87502c\"," +
                "\"from\":\"000.00.11.G14\",\"to\":\"G08\",\"status\":\"initial\"}],\"ErrorDesc\":\"Thanhcong\",\"ErrorCode\":\"0\",\"status\":\"OK\"}";

        JSONObject resultObj = new JSONObject(result);
        return result;
    }


    @PostMapping(value = "/sendEdoc", produces = "application/json",consumes = "application/octet-stream")
    public ResponseEntity<?> sendEdoc(@RequestBody byte[] fileContent, @RequestHeader Map<String, String> headers){

        JSONObject result = new JSONObject();
        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");
        result.put("DocId", UUID.randomUUID().toString());
        return ResponseEntity.ok(result.toString());
    }

    @PostMapping("getAgenciesList")
    public ResponseEntity<Object> getAgenciesList() {
        try {
            String filePath = "/opt/lgsp/integration-hub/bin/save/agencies.json";
            Reader reader = new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8);
            Object json = new Gson().fromJson(reader, Object.class);
            return ResponseEntity.ok(json);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("Lỗi đọc dữ liệu: " + e.getMessage());
        }
    }

}
