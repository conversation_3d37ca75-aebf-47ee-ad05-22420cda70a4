package com.example.integration.controller;

import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.hub.action.TepDuLieuAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import org.springframework.core.io.Resource;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/tepdulieu")
@Slf4j
@Validated
public class TepDuLieuController {
    @Autowired
    TepDuLieuAction action;
    @PostMapping(value = "/upload/file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<?> uploadFileBackup(@RequestParam("file") MultipartFile file) {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("Please upload a file.");
        }

        try {
            TepDuLieu fileSaved = action.uploadFile(file,null);
            fileSaved.setDuongDanURL("----");
            return ResponseEntity.ok(fileSaved);
        } catch (Exception ex) {
            return ResponseEntity.internalServerError().body("File upload failed: " + ex.getMessage());
        }
    }

    @GetMapping(value = "/filter")
    public ResponseEntity<?> filter(@RequestParam(name = "keyword", required = false) String keyword,
                                    @RequestParam(name = "page") Integer page, @RequestParam(name = "size") Integer size,
                                    @RequestParam(name = "loaiNguonLuuTru_MaMuc", required = false) String loaiNguonLuuTru_MaMuc,
                                    @RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
                                    @RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

        log.debug("Filter TepDuLieu: keyword: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}", keyword, page,
                size, orderFields, orderTypes);

        Page<TepDuLieu> result = action.filter(keyword, page, size,loaiNguonLuuTru_MaMuc,orderFields, orderTypes);

        return ResponseEntity.ok(result);

    }
    @GetMapping("/download/{maDinhDanh}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String maDinhDanh) {
        return action.downloadFile(maDinhDanh);
    }
}
