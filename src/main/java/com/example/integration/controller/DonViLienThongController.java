package com.example.integration.controller;

import com.example.integration.config.PropKey;
import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.dto.resp.BaseRespDTO;
import com.example.integration.dto.resp.DonViLienThongResqDTO;
import com.example.integration.entity.C_DonViLienThong;
import com.example.integration.hub.action.DonViLienThongAction;
import com.example.integration.hub.action.SDKVXPAction;
import com.example.integration.hub.action.SendNotifiAction;
import com.example.integration.hub.action.impl.SDKVXPActionImpl;
import com.example.integration.hub.service.impl.HangDoiGoiTinServiceImpl;
import com.example.integration.hub.service.impl.SchedulerService;
import com.example.integration.hub.util.FunctionCallCounter;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.ResponseUtil;
import com.fds.flex.common.utility.string.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/donvilienthong")
@Slf4j
@Validated
@ConfigurationProperties(prefix = "integration.hub.lgsp")
public class DonViLienThongController {
    @Autowired
    DonViLienThongAction action;
    @Autowired
    private HangDoiGoiTinServiceImpl hangDoiGoiTinServiceImpl;

    @Autowired
    private SendNotifiAction sendNotifiAction;

    @Autowired
    private FunctionCallCounter functionCallCounter;
    @Autowired
     SchedulerService service;
    @PostMapping(value = "", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
    public ResponseEntity<?> add(@RequestBody DonViLienThongReqDTO request) {

        log.debug("Get body create DonviLienThong: {} ", new JSONObject(request));

        C_DonViLienThong object = action.add(request);

        return ResponseEntity.ok(new DonViLienThongResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    @PostMapping(value = "/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
    public ResponseEntity<?> update(@PathVariable(name = "id") String id,
                                    @RequestBody DonViLienThongReqDTO request) {

        log.debug("Get body update DonViLienThong: {} ", new JSONObject(request));

        C_DonViLienThong object = action.update(id, request);

        return ResponseEntity.ok(new DonViLienThongResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<?> findById(@PathVariable(name = "id") String id) {

        log.debug("Find DonViLienThong: {} ", id);

        C_DonViLienThong donViLienThong = action.findById(id);

        return ResponseEntity.ok(new DonViLienThongResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                null, donViLienThong));

    }

    @DeleteMapping(value = "/{id}")
    public ResponseEntity<?> delete(@PathVariable(name = "id") String id) {

        log.debug("Delete DonViLienThong: {} ", id);

        action.delete(id);

        return ResponseEntity.ok(new BaseRespDTO<String, String>(ResponseUtil.RespCode.SUCCESS.getCode(), "success",
                "success", id, "Delete success"));

    }


    @GetMapping(value = "/filterSecondLevel")
    public ResponseEntity<?> filterSecondLevel(@RequestParam(name = "keyword", required = false) String keyword,
                                    @RequestParam(name = "page") Integer page, @RequestParam(name = "size") Integer size,
                                    @RequestParam(name = "tenMuc", required = false) String tenMuc,
                                    @RequestParam(name = "maMuc", required = false) String maMuc,
                                    @RequestParam(name = "tuNgay", required = false) Long tuNgay,
                                    @RequestParam(name = "denNgay", required = false) Long denNgay,
                                    @RequestParam(name = "ID_DVLT", required = false) String id,
                                    @RequestParam(name = "ma", required = false) String ma,
                                    @RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
                                    @RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

        log.debug("Filter DonVILienThong: keyword: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}", keyword, page,
                size, orderFields, orderTypes);

        Page<C_DonViLienThong> result = action.filterSecondLevel(keyword, page, size,tenMuc,maMuc,tuNgay,denNgay,id,ma,orderFields, orderTypes);

        return ResponseEntity.ok(result);

    }


    @GetMapping(value = "/dongbodvlt")

    public ResponseEntity<?> dongbodvlt(@RequestParam(name = "dongBo", required = false) Boolean dongBo) {
        String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);

        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
        log.info("Bắt đầu đồng bộ danh sách đơn vị liên thông");
        try {
            service.getListAgencies(sdkVxpAction);
            log.info("Kết thúc đồng bộ danh sách đơn vị liên thông");
        } catch (Exception e) {
            log.error("Lỗi khi đồng bộ danh sách đơn vị liên thông: {}", e.getMessage());
        }
        return null;
    }

}

