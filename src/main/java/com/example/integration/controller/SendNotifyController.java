package com.example.integration.controller;

import com.example.integration.hub.service.impl.SchedulerService;
import com.example.integration.hub.util.FunctionCallCounter;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/integration/1.0/sendnoti") // API endpoint
public class SendNotifyController {
    @Autowired
    private SchedulerService schedulerService;

    @Autowired
    private FunctionCallCounter functionCallCounter;

    @PostMapping(value = "/vanban", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Send notification for van ban", description = "Manually trigger the sendNotificationForVanBan function")
    public ResponseEntity<?> sendNotificationForVanBan(@RequestParam(required = false) String status) {
        try {

            if (status == null) {

                Map<String, Object> response = new HashMap<>();
                response.put("status", "error");
                response.put("message", "Status parameter is required");
                return ResponseEntity.badRequest().body(response);
            }

            schedulerService.sendNotificationForVanBan(status);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Notification for van ban sent successfully");

            return ResponseEntity.ok().body(response);
        } catch (Exception e) {

            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send notification: " + e.getMessage());

            return ResponseEntity.status(500).body(response);
        }
    }


    @PostMapping(value = "/vanbantre", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Send notification for documents that are one day late", description = "Manually trigger the runNotifySendOneDayLate function")
    public ResponseEntity<?> notifySendOneDayLate() {
        try {
            schedulerService.runNotifySendOneDayLate();
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Notification for late documents sent successfully");
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send notification: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping(value = "/functioncalls", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Send notification about function call counts", description = "Manually trigger the sendFunctionCallCountNotification function")
    public ResponseEntity<?> sendFunctionCallCountNotification() {
        try {
            schedulerService.sendFunctionCallCountNotification();
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Function call count notification sent successfully");
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send notification: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping(value = "/onehour", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Send notification about documents processed in the last hour", description = "Manually trigger the runNotifyGuiNhan1Hour function")
    public ResponseEntity<?> notifyGuiNhan1Hour() {
        try {
            schedulerService.runNotifyGuiNhan1Hour();
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "One-hour statistics notification sent successfully");
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send notification: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping(value = "/increment/{functionType}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Increment function call counter", description = "Manually increment the function call counter for a specific function type (GET, SEND, UPDATE)")
    public ResponseEntity<?> incrementFunctionCallCounter(@PathVariable String functionType) {
        try {
            // Validate function type
            if (!functionType.equals(FunctionCallCounter.FUNCTION_GET) &&
                !functionType.equals(FunctionCallCounter.FUNCTION_SEND) &&
                !functionType.equals(FunctionCallCounter.FUNCTION_UPDATE)) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", "error");
                response.put("message", "Invalid function type. Must be one of: GET, SEND, UPDATE");
                return ResponseEntity.badRequest().body(response);
            }

            // Increment the counter
            functionCallCounter.incrementCount(functionType);

            // Get the current count
            int currentCount = functionCallCounter.getCount(functionType);

            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Function call counter for " + functionType + " incremented successfully");
            response.put("currentCount", currentCount);
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to increment function call counter: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
