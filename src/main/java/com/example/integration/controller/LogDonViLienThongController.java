package com.example.integration.controller;

import com.example.integration.dto.req.LogDonViLienThongReqDTO;
import com.example.integration.dto.resp.BaseRespDTO;
import com.example.integration.dto.resp.LogDonViLienThongResqDTO;
import com.example.integration.entity.T_Log_DonViLienThong;
import com.example.integration.hub.action.LogDonViLienThongAction;
import com.fds.flex.common.ultility.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/LogDonViLienThong")
@Slf4j
@Validated
public class LogDonViLienThongController {
    @Autowired
    LogDonViLienThongAction action;

    @PostMapping(value = "", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
    public ResponseEntity<?> add(@RequestBody LogDonViLienThongReqDTO request) {

        log.debug("Get body create LogDonViLienThong: {} ", new JSONObject(request));

        T_Log_DonViLienThong object = action.add(request);

        return ResponseEntity.ok(new LogDonViLienThongResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    @PostMapping(value = "/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
    public ResponseEntity<?> update(@PathVariable(name = "id") String id,
                                    @RequestBody LogDonViLienThongReqDTO request) {

        log.debug("Get body update LogDonViLienThong: {} ", new JSONObject(request));

        T_Log_DonViLienThong object = action.update(id, request);

        return ResponseEntity.ok(new LogDonViLienThongResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<?> findById(@PathVariable(name = "id") String id) {

        log.debug("Find LogDonViLienThong: {} ", id);

        T_Log_DonViLienThong LogDonViLienThong = action.findById(id);

        return ResponseEntity.ok(new LogDonViLienThongResqDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                null, LogDonViLienThong));

    }

    @DeleteMapping(value = "/{id}")
    public ResponseEntity<?> delete(@PathVariable(name = "id") String id) {

        log.debug("Delete LogDonViLienThong: {} ", id);

        action.delete(id);

        return ResponseEntity.ok(new BaseRespDTO<String, String>(ResponseUtil.RespCode.SUCCESS.getCode(), "success",
                "success", id, "Delete success"));

    }

    @GetMapping(value = "/filter")
    public ResponseEntity<?> filter(@RequestParam(name = "keyword", required = false) String keyword,
                                    @RequestParam(name = "page") Integer page, @RequestParam(name = "size") Integer size,
                                    @RequestParam(name = "tuNgay", required = false) Long  tuNgay,
                                    @RequestParam(name = "denNgay", required = false) Long  denNgay,
                                    @RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
                                    @RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

        log.debug("Filter Log_DonViLienThong: keyword: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}", keyword, page,
                size, orderFields, orderTypes);

        Page<T_Log_DonViLienThong> result = action.filter(keyword, page, size,tuNgay,denNgay,orderFields, orderTypes);

        return ResponseEntity.ok(result);

    }
}
