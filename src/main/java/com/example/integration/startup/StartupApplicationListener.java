//package com.example.integration.startup;
//
//import com.example.integration.config.PropKey;
//import com.example.integration.hub.scheduler.GetAgencyScheduler;
//import com.example.integration.hub.scheduler.TaskSchedulingService;
//import com.fds.flex.common.ultility.GetterUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.context.event.ContextRefreshedEvent;
//import org.springframework.stereotype.Component;
//
//@Component
//@Slf4j
//public class StartupApplicationListener implements ApplicationListener<ContextRefreshedEvent> {
//	@Autowired
//	@Lazy
//	private TaskSchedulingService taskSchedulingService;
//
//	@Autowired
//	@Lazy
//	private GetAgencyScheduler getAgencyScheduler;
//    @Override
//    public void onApplicationEvent(ContextRefreshedEvent event) {
//		String newCrontab = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_SCHEDULER_GET_AGENCY_CRON), "0 0 2 * * *");
//		log.info("onApplicationEvent...install getAgencyCron : " + newCrontab);
//        Runnable task = () -> getAgencyScheduler.scheduleGetListAgencies();
//        taskSchedulingService.scheduleATask(PropKey.INTEGRATION_HUB_SCHEDULER_GET_AGENCY_CRON, task, newCrontab);
//		log.info("onApplicationEvent...installed getAgencyCron : " + newCrontab);
//    }
//}
