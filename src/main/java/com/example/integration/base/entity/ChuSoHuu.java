package com.example.integration.base.entity;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
public class ChuSoHuu {
	@JsonProperty("@type")
	@Field(value = DBConstant.TYPE, order = 0)
	public String type = DBConstant.T_DANH_TINH_DIEN_TU;

	@JsonProperty("MaDinhDanh")
	@Field(value = "MaDinhDanh", order = 1)
	public String maDinhDanh = StringPool.BLANK;

	@JsonProperty("TenDinhDanh")
	@Field(value = "TenDinhDanh", order = 2)
	public String tenDinhDanh = StringPool.BLANK;

	@JsonProperty("TenGoi")
	@Field(value = "TenGoi", order = 3)
	public String tenGoi = StringPool.BLANK;
}
