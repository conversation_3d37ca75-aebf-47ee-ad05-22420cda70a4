package com.example.integration.base.entity;

import com.example.integration.constant.DBConstant;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChiaSeTaiKhoan {
	@JsonProperty("@type")
	@Field(value = DBConstant.TYPE, order = 0)
	public String type = DBConstant.S_CHIA_SE_TAI_KHOAN;

	@JsonProperty("DanhTinhDienTu")
	@Field(value = "DanhTinhDienTu", order = 1)
	public DanhTinhDienTu danhTinhDienTu = new DanhTinhDienTu();

	@JsonProperty("QuyenDoc")
	@Field(value = "QuyenDoc", order = 2)
	public boolean quyenDoc = false;

	@JsonProperty("QuyenGhi")
	@Field(value = "QuyenGhi", order = 3)
	public boolean quyenGhi = false;

	@JsonProperty("QuyenXoa")
	@Field(value = "QuyenXoa", order = 3)
	public boolean quyenXoa = false;

	@Getter
	@Setter
	public static class DanhTinhDienTu {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.T_DANH_TINH_DIEN_TU;

		@JsonProperty("MaDinhDanh")
		@Field(value = "MaDinhDanh", order = 1)
		public String maDinhDanh = StringPool.BLANK;

		@JsonProperty("TenGoi")
		@Field(value = "TenGoi", order = 2)
		public String tenGoi = StringPool.BLANK;
	}
}
