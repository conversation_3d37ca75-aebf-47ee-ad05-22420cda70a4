package com.example.integration.base.entity;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;

import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class TrangThaiDuLieu {
	@JsonProperty("@type")
	@Field(value = DBConstant.TYPE, order = 0)
	public String type = DBConstant.C_TRANG_THAI_DU_LIEU;

	@JsonProperty("MaMuc")
	@Field(value = "MaMuc", order = 1)
	public String maMuc = TrangThai.ChinhThuc.maMuc;

	@JsonProperty("TenMuc")
	@Field(value = "TenMuc", order = 2)
	public String tenMuc = TrangThai.ChinhThuc.tenMuc;

	public TrangThaiDuLieu init(String maMuc) {
		TrangThaiDuLieu trangThaiDuLieu = new TrangThaiDuLieu();
		if (maMuc.equals(TrangThaiDuLieu.TrangThai.ChinhThuc.getMaMuc())) {
			trangThaiDuLieu.setMaMuc(TrangThaiDuLieu.TrangThai.ChinhThuc.getMaMuc());
			trangThaiDuLieu.setTenMuc(TrangThaiDuLieu.TrangThai.ChinhThuc.getTenMuc());
		} else if (maMuc.equals(TrangThaiDuLieu.TrangThai.SoBo.getMaMuc())) {
			trangThaiDuLieu.setMaMuc(TrangThaiDuLieu.TrangThai.SoBo.getMaMuc());
			trangThaiDuLieu.setTenMuc(TrangThaiDuLieu.TrangThai.SoBo.getTenMuc());
		} else if (maMuc.equals(TrangThaiDuLieu.TrangThai.HuyBo.getMaMuc())) {
			trangThaiDuLieu.setMaMuc(TrangThaiDuLieu.TrangThai.HuyBo.getMaMuc());
			trangThaiDuLieu.setTenMuc(TrangThaiDuLieu.TrangThai.HuyBo.getTenMuc());
		} else if (maMuc.equals(TrangThaiDuLieu.TrangThai.LichSu.getMaMuc())) {
			trangThaiDuLieu.setMaMuc(TrangThaiDuLieu.TrangThai.LichSu.getMaMuc());
			trangThaiDuLieu.setTenMuc(TrangThaiDuLieu.TrangThai.LichSu.getTenMuc());
		}

		return trangThaiDuLieu;
	}

	@Getter
	public enum TrangThai {
		SoBo("01", "Sơ Bộ"), ChinhThuc("02", "Chính Thức"), LichSu("03", "Lịch Sử"), HuyBo("04", "Hủy Bỏ"),
		CanXoa("05", "Cần xóa");

		public String maMuc = StringPool.BLANK;
		public String tenMuc = StringPool.BLANK;

		TrangThai(String maMuc, String tenMuc) {
			setMaMuc(maMuc);
			setTenMuc(tenMuc);
		}

		public void setMaMuc(String maMuc) {
			this.maMuc = maMuc;
		}

		public void setTenMuc(String tenMuc) {
			this.tenMuc = tenMuc;
		}

	}
}
