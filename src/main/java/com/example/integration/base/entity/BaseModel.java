package com.example.integration.base.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fds.flex.common.customformat.CustomDateDeserializer;
import com.fds.flex.common.customformat.CustomDateSerializer;
import com.fds.flex.common.ultility.UuidGenerator;
import com.fds.flex.common.utility.datetime.DateTimeUtils;
import com.fds.flex.common.utility.string.StringPool;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;
import org.springframework.data.mongodb.core.mapping.MongoId;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BaseModel<T extends Serializable> implements Serializable, Cloneable {
	private static final long serialVersionUID = 1L;

	@Id
	@MongoId(value = FieldType.OBJECT_ID)
	@Field(value = "_id", order = 0)
	@JsonIgnore // khong convert khi doc nguoc lai String -> Object
	public ObjectId _id;

	@Transient // -> ko mapping vao db
	public String primKey;

	@Transient // -> ko mapping vao db
	@JsonIgnore
	public boolean isUpdate = false;

	@JsonProperty("@type")
	@Field(value = DBConstant.TYPE, order = 2)
	public String type;

	@JsonProperty("uuid")
	@Field(value = "uuid", order = 3)
	public String uuid;

	@JsonProperty("ThoiGianTao")
	@JsonSerialize(using = CustomDateSerializer.class)
	@JsonDeserialize(using = CustomDateDeserializer.class)
	@Field(value = "ThoiGianTao", order = 994)
	@CreatedDate
	public Long thoiGianTao;

	@JsonProperty("ThoiGianCapNhat")
	@JsonSerialize(using = CustomDateSerializer.class)
	@JsonDeserialize(using = CustomDateDeserializer.class)
	@Field(value = "ThoiGianCapNhat", order = 995)
	@LastModifiedDate
	public Long thoiGianCapNhat;

	@JsonProperty("NguoiTaoLap")
	@Field(value = "NguoiTaoLap", order = 996)
	@CreatedBy
	public NguoiTaoLap nguoiTaoLap;

	@JsonProperty("ChuSoHuu")
	@Field(value = "ChuSoHuu", order = 997)
	public ChuSoHuu chuSoHuu = null;

	@JsonProperty("TrangThaiDuLieu")
	@Field(value = "TrangThaiDuLieu", order = 998)
	public TrangThaiDuLieu trangThaiDuLieu = new TrangThaiDuLieu();

	@JsonProperty("NguonThamChieu")
	@Field(value = "NguonThamChieu", order = 1000)
	public List<NguonThamChieu> nguonThamChieu = new ArrayList<>();

	@JsonProperty("NhatKiSuaDoi")
	@Field(value = "NhatKiSuaDoi", order = 1001)
	public List<NhatKiSuaDoi> nhatKiSuaDoi = new ArrayList<>();

	@JsonProperty("LienKetURL")
	@Field(value = "LienKetURL", order = 1002)
	public String lienKetURL = StringPool.BLANK;

	@JsonProperty("ChiaSeTaiKhoan")
	@Field(value = "ChiaSeTaiKhoan", order = 1007)
	public List<ChiaSeTaiKhoan> chiaSeTaiKhoan = new ArrayList<>();

	@JsonProperty("ChiaSeVaiTro")
	@Field(value = "ChiaSeVaiTro", order = 1008)
	public List<ChiaSeVaiTro> chiaSeVaiTro = new ArrayList<>();

	@JsonProperty("MaPhienBan")
	@Field(value = "MaPhienBan", order = 999)
	public String maPhienBan = "1";

	@JsonProperty("MaDinhDanhThayThe")
	@Field(value = "MaDinhDanhThayThe", order = 1003)
	public String maDinhDanhThayThe = StringPool.BLANK;

	@JsonProperty("PhanVungDuLieu")
	@Field(value = "PhanVungDuLieu", order = 1006)
	public PhanVungDuLieu phanVungDuLieu = new PhanVungDuLieu();

	public BaseModel(boolean isUpdate) {
		this.isUpdate = isUpdate;
		if (!isUpdate) {
			this.setUuid(UuidGenerator.random(this.getClass()));
		}
	}

	public T clone() throws CloneNotSupportedException {
		T obj = (T) super.clone();

		return obj;
	}

	public T cloneExclude(String[] filedNames) throws CloneNotSupportedException {
		T obj = clone();
		if (filedNames != null) {
			try {
				for (String filedName : filedNames) {

					obj.getClass().getField(filedName).set(obj, null);
				}
			} catch (Exception e) {
				log.warn(e.getMessage());
			}
		}
		return obj;
	}

	public T cloneWithUpdate(Object... data) throws CloneNotSupportedException {
		T obj = clone();

		if (data != null && data.length % 2 == 0) {
			try {
				for (int i = 0; i < data.length; i = i + 2) {
					String filedName = (String) data[i];
					Object value = data[i + 1];
					log.info("cloneWithUpdate: field = {}, value = {}", filedName, value);
					obj.getClass().getField(filedName).set(obj, value);
				}
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage());
			}
		} else {
			log.error("data array is null or data length is not even number");
		}
		return obj;
	}

	public T update(Object obj, Object... data) {
		if (data != null && data.length % 2 == 0) {
			try {
				for (int i = 0; i < data.length; i = i + 2) {
					String filedName = (String) data[i];
					Object value = data[i + 1];
					log.info("update: field = {}, value = {}", filedName, value);
					obj.getClass().getField(filedName).set(obj, value);
				}
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage());
			}
		} else {
			log.error("data array is null or data length is not even number");
		}

		return (T) obj;
	}

	@Setter
	@Getter
	public static class VaiTroSuDung {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.C_VAI_TRO_SU_DUNG;

		@JsonProperty("MaMuc")
		@Field(value = "MaMuc", order = 1)
		public String maMuc = StringPool.BLANK;

		@JsonProperty("TenMuc")
		@Field(value = "TenMuc", order = 2)
		public String tenMuc = StringPool.BLANK;
	}

	@Setter
	@Getter
	public static class NhatKiSuaDoi {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.S_NHAT_KY_SUA_DOI;

		@JsonProperty("ThoiGian")
		@Field(value = "ThoiGian", order = 1)
		@JsonFormat(pattern = DateTimeUtils._GLOBAL_TIME_FORMAT, locale = "vi_VN", timezone = "Asia/Ho_Chi_Minh")
		public Date thoiGian;

		@JsonProperty("MaPhienBan")
		@Field(value = "MaPhienBan", order = 2)
		public String maPhienBan = "1";

		@JsonProperty("TacGia")
		@Field(value = "TacGia", order = 3)
		public String tacGia = StringPool.BLANK;

		@JsonProperty("NoiDungSuaDoi")
		@Field(value = "NoiDungSuaDoi", order = 4)
		public String noiDungSuaDoi = StringPool.BLANK;

		@JsonProperty("MaSoBanTin")
		@Field(value = "MaSoBanTin", order = 5)
		public String maSoBanTin = StringPool.BLANK;

		@JsonProperty("SoChungThu")
		@Field(value = "SoChungThu", order = 6)
		public String soChungThu = StringPool.BLANK;

		@JsonProperty("NguoiKySo")
		@Field(value = "NguoiKySo", order = 7)
		public String nguoiKySo = StringPool.BLANK;

		@JsonProperty("NgayKySo")
		@Field(value = "NgayKySo", order = 8)
		@JsonFormat(pattern = DateTimeUtils._GLOBAL_TIME_FORMAT, locale = "vi_VN", timezone = "Asia/Ho_Chi_Minh")
		public Date ngayKySo;

		@JsonProperty("HanHieuLuc")
		@Field(value = "HanHieuLuc", order = 9)
		@JsonFormat(pattern = DateTimeUtils._GLOBAL_TIME_FORMAT, locale = "vi_VN", timezone = "Asia/Ho_Chi_Minh")
		public Date hanHieuLuc;

		@JsonProperty("LyDoKhoiPhuc")
		@Field(value = "LyDoKhoiPhuc", order = 10)
		public String lyDoKhoiPhuc = StringPool.BLANK;
	}

	@Setter
	@Getter
	public static class NguonThamChieu {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.S_NGUON_THAM_CHIEU;

		@JsonProperty("MaNguonDuLieu")
		@Field(value = "MaNguonDuLieu", order = 1)
		public String maNguonDuLieu = StringPool.BLANK;

		@JsonProperty("MaThamChieu")
		@Field(value = "MaThamChieu", order = 2)
		public String maThamChieu = StringPool.BLANK;

		@JsonProperty("LoDuLieu")
		@Field(value = "LoDuLieu", order = 3)
		public String loDuLieu = StringPool.BLANK;

		@JsonProperty("ThoiGianCapNhat")
		@JsonFormat(pattern = DateTimeUtils._GLOBAL_TIME_FORMAT, locale = "vi_VN", timezone = "Asia/Ho_Chi_Minh")
		@Field(value = "ThoiGianCapNhat", order = 4)
		public Date thoiGianCapNhat = new Date();

	}

	@Setter
	@Getter
	public static class PhanVungDuLieu {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.C_PHAN_VUNG_DU_LIEU;

		@JsonProperty("MaMuc")
		@Field(value = "MaMuc", order = 1)
		public String maMuc = StringPool.BLANK;

		@JsonProperty("TenMuc")
		@Field(value = "TenMuc", order = 2)
		public String tenMuc = StringPool.BLANK;
	}

}
