package com.example.integration.base.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
public class NguoiTaoLap {
	@JsonProperty("@type")
	@Field(value = "@type", order = 0)
	public String type = "T_DanhTinhDienTu";

	@JsonProperty("MaDinhDanh")
	@Field(value = "MaDinhDanh", order = 1)
	public String maDinhDanh = "";

	@JsonProperty("TenDinhDanh")
	@Field(value = "TenDinhDanh", order = 2)
	public String tenDinhDanh = "";

	@JsonProperty("TenGoi")
	@Field(value = "TenGoi", order = 3)
	public String tenGoi = "";
}
