package com.example.integration.base.entity;

import java.io.Serializable;

import com.example.integration.constant.DBConstant;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fds.flex.common.customformat.CustomDateDeserializer;
import com.fds.flex.common.customformat.CustomDateSerializer;
import com.fds.flex.common.customformat.CustomUTCDateDeserializer;
import com.fds.flex.common.customformat.CustomUTCDateSerializer;
import com.fds.flex.common.utility.string.StringPool;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;
import org.springframework.data.mongodb.core.mapping.MongoId;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class BaseCategory<T extends Serializable> implements Serializable, Cloneable {
    private static final long serialVersionUID = 1L;

    @Id
    @MongoId(value = FieldType.OBJECT_ID)
    @Field(value = "_id", order = 0)
    @JsonIgnore // khong convert khi doc nguoc lai String -> Object
    public ObjectId _id;

    @Transient // -> ko mapping vao db
    public String primKey;

    @Transient // -> ko mapping vao db
    @JsonIgnore
    public boolean isUpdate = false;

    @JsonProperty("@type")
    @Field(value = DBConstant.TYPE, order = 2)
    public String type;

    @JsonProperty("ThoiGianTao")
    @JsonSerialize(using = CustomDateSerializer.class)
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @Field(value = "ThoiGianTao", order = 996)
    @CreatedDate
    public Long thoiGianTao = null;

    @JsonProperty("ThoiGianCapNhat")
    @JsonSerialize(using = CustomUTCDateSerializer.class)
    @JsonDeserialize(using = CustomUTCDateDeserializer.class)
    @Field(value = "ThoiGianCapNhat", order = 997)
    @LastModifiedDate
    public Long thoiGianCapNhat;

    @JsonProperty("TrangThaiDuLieu")
    @Field(value = "TrangThaiDuLieu", order = 998)
    public TrangThaiDuLieu trangThaiDuLieu = new TrangThaiDuLieu();

    @JsonProperty("MaMuc")
    @Field(value = "MaMuc", order = 4)
    public String maMuc = StringPool.BLANK;

    @JsonProperty("TenMuc")
    @Field(value = "TenMuc", order = 5)
    public String tenMuc = StringPool.BLANK;

    @JsonProperty("MaPhienBan")
    @Field(value = "MaPhienBan", order = 999)
    public String maPhienBan = null;

    @JsonIgnore
    @Transient // -> ko mapping vao db
    private Long id;

    public BaseCategory(boolean isUpdate) {
    }

    public T clone() throws CloneNotSupportedException {

        return (T) super.clone();
    }

    public T cloneExclude(String[] filedNames) throws CloneNotSupportedException {
        T obj = clone();
        if (filedNames != null) {
            try {
                for (String filedName : filedNames) {

                    obj.getClass().getField(filedName).set(obj, null);
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
        return obj;
    }

    public T cloneWithUpdate(Object... data) throws CloneNotSupportedException {
        T obj = clone();

        if (data != null && data.length % 2 == 0) {
            try {
                for (int i = 0; i < data.length; i = i + 2) {
                    String filedName = (String) data[i];
                    Object value = data[i + 1];
                    log.info("cloneWithUpdate: field = {}, value = {}", filedName, value);
                    obj.getClass().getField(filedName).set(obj, value);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage());
            }
        } else {
            log.error("data array is null or data length is not even number");
        }
        return obj;
    }

    public T update(Object obj, Object... data) {
        if (data != null && data.length % 2 == 0) {
            try {
                for (int i = 0; i < data.length; i = i + 2) {
                    String filedName = (String) data[i];
                    Object value = data[i + 1];
                    log.info("update: field = {}, value = {}", filedName, value);
                    obj.getClass().getField(filedName).set(obj, value);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage());
            }
        } else {
            log.error("data array is null or data length is not even number");
        }

        return (T) obj;
    }
}
