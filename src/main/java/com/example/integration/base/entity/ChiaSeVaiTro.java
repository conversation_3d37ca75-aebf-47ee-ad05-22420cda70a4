package com.example.integration.base.entity;

import com.example.integration.constant.DBConstant;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fds.flex.common.utility.string.StringPool;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChiaSeVaiTro {
	@JsonProperty("@type")
	@Field(value = DBConstant.TYPE, order = 0)
	public String type = DBConstant.S_CHIA_SE_VAI_TRO;

	@JsonProperty("VaiTroSuDung")
	@Field(value = "VaiTroSuDung", order = 1)
	public VaiTroSuDung vaiTroSuDung = new VaiTroSuDung();

	@JsonProperty("QuyenDoc")
	@Field(value = "QuyenDoc", order = 2)
	public boolean quyenDoc = false;

	@JsonProperty("QuyenGhi")
	@Field(value = "QuyenGhi", order = 3)
	public boolean quyenGhi = false;

	@JsonProperty("QuyenXoa")
	@Field(value = "QuyenXoa", order = 3)
	public boolean quyenXoa = false;

	@Getter
	@Setter
	public static class VaiTroSuDung {
		@JsonProperty("@type")
		@Field(value = DBConstant.TYPE, order = 0)
		public String type = DBConstant.C_VAI_TRO_SU_DUNG;

		@JsonProperty("MaMuc")
		@Field(value = "MaMuc", order = 1)
		private String maMuc = StringPool.BLANK;

		@JsonProperty("TenMuc")
		@Field(value = "TenMuc", order = 2)
		private String tenMuc = StringPool.BLANK;
	}
}

