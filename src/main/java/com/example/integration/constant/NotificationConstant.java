package com.example.integration.constant;

public class NotificationConstant {

    public static final String USER_HAS_NOT_PERMISSION_TO_DO_THIS_ACTION = "User.Permission.not_action";
    public static final String USER_HAS_NOT_PERMISSION_TO_ACCESS_ADMIN_API = "User.Permission.cannot_access_admin_api";
    public static final String USER_HAS_NOT_PERMISSION_TO_ACCESS_INTERNAL_API = "User.Permission.cannot_access_internal_api";
    public static final String USER_HAS_NOT_PERMISSION_TO_ACCESS_EXPOSED_API = "User.Permission.cannot_access_exposed_api";
    public static final String REQUESTED_URI_NOT_ACCEPT = "Request.uri.not_accept";

    public static final String TAINGUYENHETHONG_ERROR_WHEN_MAPPING_OBJECT = "TaiNguyenHeThong.Error_mapping_object";
    public static final String PHANQUYEN_DANHTINHDIENTU_ERROR_WHEN_GET_DETAIL = "PhanQuyen.DanhTinhDienTu.Error_get_detail";
    public static final String RESCONNDECLARE_BEARERTOKEN_USERNAME_PASSWORD_INCORRECT = "ResConnDeclare.BearerToken.usernamePassword_incorrect";

    public static final String KIEU_LOAI_GOI_TIN_INCORRECT = "kieuloaigoitin.incorrect";
    public static final String REFRESH_TOKEN_NULL_EMPTY = "refresh_token.null_empty";
    public static final String SYS_AUTH_LOGIN_FAILURE = "sys_auth.login_failure";
    public static final String SYS_AUTH_CANNOT_CONNECT = "sys_auth.cannot_connect";
    public static final String LOGIN_INFO_NULL_EMPTY = "login_info.null_empty";

    public static final String MONITOR_LAYOUT_ID_NULL_EMPTY = "monitor_layout.id.null_empty";
    public static final String MONITOR_LAYOUT_DOCUMENT_NOT_FOUND = "monitor_layout.document.not_found";
    public static final String MONITOR_LAYOUT_CODE_EXIST_CONFLICT = "monitor_layout.code.exist_conflict";

    public static final String MONITOR_LAYOUT_TEMPLATE_ID_NULL_EMPTY = "monitor_layout_template.id.null_empty";
    public static final String MONITOR_LAYOUT_TEMPLATE_DOCUMENT_NOT_FOUND = "monitor_layout_template.document.not_found";
    public static final String MONITOR_LAYOUT_TEMPLATE_CODE_EXIST_CONFLICT = "monitor_layout_template.code.exist_conflict";

    public static final String EMAIL_TEMPLATE_ID_NULL_EMPTY = "email_template.id.null_empty";
    public static final String EMAIL_TEMPLATE_DOCUMENT_NOT_FOUND = "email_template.document.not_found";
    public static final String EMAIL_TEMPLATE_CODE_EXIST_CONFLICT = "email_template.code.exist_conflict";

    public static final String MONITOR_DASHBOARD_ID_NULL_EMPTY = "monitor_dashboard.id.null_empty";
    public static final String MONITOR_DASHBOARD_DOCUMENT_NOT_FOUND = "monitor_dashboard.document.not_found";
    public static final String MONITOR_DASHBOARD_CODE_EXIST_CONFLICT = "monitor_dashboard.code.exist_conflict";

    public static final String MONITOR_DASHBOARD_TEMPLATE_ID_NULL_EMPTY = "monitor_dashboard_template.id.null_empty";
    public static final String MONITOR_DASHBOARD_TEMPLATE_DOCUMENT_NOT_FOUND = "monitor_dashboard_template.document.not_found";
    public static final String MONITOR_DASHBOARD_TEMPLATE_CODE_EXIST_CONFLICT = "monitor_dashboard_template.code.exist_conflict";

    public static final String STATISTIC_DASHBOARD_ID_NULL_EMPTY = "statistic_dashboard.id.null_empty";
    public static final String STATISTIC_DASHBOARD_DOCUMENT_NOT_FOUND = "statistic_dashboard.document.not_found";
    public static final String STATISTIC_DASHBOARD_CODE_EXIST_CONFLICT = "statistic_dashboard.code.exist_conflict";

    public static final String STATISTIC_DASHBOARD_TEMPLATE_ID_NULL_EMPTY = "statistic_dashboard_template.id.null_empty";
    public static final String STATISTIC_DASHBOARD_TEMPLATE_DOCUMENT_NOT_FOUND = "statistic_dashboard_template.document.not_found";
    public static final String STATISTIC_DASHBOARD_TEMPLATE_CODE_EXIST_CONFLICT = "statistic_dashboard_template.code.exist_conflict";

    public static final String STATISTIC_LAYOUT_ID_NULL_EMPTY = "statistic_layout.id.null_empty";
    public static final String STATISTIC_LAYOUT_DATE_ERROR = "statistic_layout.date.error";
    public static final String STATISTIC_LAYOUT_DOCUMENT_NOT_FOUND = "statistic_layout.document.not_found";
    public static final String STATISTIC_LAYOUT_CODE_EXIST_CONFLICT = "statistic_layout.code.exist_conflict";
    
    public static final String SERVICE_HEALTH_CHECK_HISTORY_ID_NULL_EMPTY = "statistic_layout.id.null_empty";
    public static final String SERVICE_HEALTH_CHECK_HISTORY_DOCUMENT_NOT_FOUND = "statistic_layout.document.not_found";
    public static final String SERVICE_HEALTH_CHECK_HISTORY_CODE_EXIST_CONFLICT = "statistic_layout.code.exist_conflict";
    
    public static final String SERVICE_HEALTH_CHECK_ID_NULL_EMPTY = "service_health_check.id.null_empty";
    public static final String SERVICE_HEALTH_CHECK_DOCUMENT_NOT_FOUND = "service_health_check.document.not_found";
    public static final String SERVICE_HEALTH_CHECK_CODE_EXIST_CONFLICT = "service_health_check.code.exist_conflict";

    public static final String STATISTIC_LAYOUT_TEMPLATE_ID_NULL_EMPTY = "statistic_layout_template.id.null_empty";
    public static final String STATISTIC_LAYOUT_TEMPLATE_DOCUMENT_NOT_FOUND = "statistic_layout_template.document.not_found";
    public static final String STATISTIC_LAYOUT_TEMPLATE_CODE_EXIST_CONFLICT = "statistic_layout_template.code.exist_conflict";
    
    public static final String DATA_SOURCE_ID_NULL_EMPTY = "data_source.id.null_empty";
    public static final String DATA_SOURCE_CODE_NULL_EMPTY = "data_source.code.null_empty";
    public static final String DATA_SOURCE_DOCUMENT_NOT_FOUND = "data_source.document.not_found";
    public static final String DATA_SOURCE_CODE_EXIST_CONFLICT = "data_source.code.exist_conflict";
    
    public static final String NOTIFICATION_RULE_ID_NULL_EMPTY = "notification_rule.id.null_empty";
    public static final String NOTIFICATION_RULE_DOCUMENT_NOT_FOUND = "notification_rule.document.not_found";
    public static final String NOTIFICATION_RULE_CODE_EXIST_CONFLICT = "notification_rule.code.exist_conflict";
    
    public static final String API_STATISTIC_ID_NULL_EMPTY = "api_statistic.id.null_empty";
    public static final String API_STATISTIC_DOCUMENT_NOT_FOUND = "api_statistic.document.not_found";

    public static final String APPLICATION_STATISTIC_ID_NULL_EMPTY = "application_statistic.id.null_empty";
    public static final String APPLICATION_STATISTIC_DOCUMENT_NOT_FOUND = "application_statistic.document.not_found";

    public static final String USER_IP_STATISTIC_ID_NULL_EMPTY = "user_ip_statistic.id.null_empty";
    public static final String USER_IP_STATISTIC_DOCUMENT_NOT_FOUND = "user_ip_statistic.document.not_found";

    public static final String IP_BLACK_LIST_ID_NULL_EMPTY = "ip_black_list.id.null_empty";
    public static final String IP_BLACK_LIST_DOCUMENT_NOT_FOUND = "ip_black_list.document.not_found";
    public static final String IP_BLACK_LIST_CODE_EXIST_CONFLICT = "ip_black_list.code.exist_conflict";

    public static final String MESSAGE_ID_NULL_EMPTY = "message.id.null_empty";
    public static final String MESSAGE_DOCUMENT_NOT_FOUND = "message.document.not_found";
    public static final String MESSAGE_CODE_EXIST_CONFLICT = "message.code.exist_conflict";

    public static final String UNIT_CODE_EXIST_CONFLICT = "unit.code.exist_conflict";
    public static final String UNIT_NOT_FOUND = "UNIT.NOT.FOUND";
    public static final String CRAWL_API_NOT_SUCCESSFUL = "crawl.api.not.successful";

    public static final String APPLICATION_CODE_EXIST_CONFLICT = "application.code.exist_conflict";
    
    public static final String DATADIODE_MESSAGE_INCORRECT = "datadiode.message.incorrect";

    
}
