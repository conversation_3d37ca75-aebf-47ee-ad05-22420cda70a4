package com.example.integration.constant;

public class Constant {
    public static final String INSENSITIVE = "i";
    public static final String SUB = "sub";
    public static final String LANGUAGE = "language";
    public static final String JTI = "jti";

    public static final String CLAIMS = "claims";

    public static final int DEFAULT_MIN_PAGE_SIZE = 20;
    public static final int DEFAULT_MAX_PAGE_SIZE = 50;
    public static final int DEFAULT_MAX_PAGE_SIZE_100 = 100;
    public static final String AUTHORIZATION = "Authorization";
    public static final String BEARER_TYPE = "Bearer";

    public static final String TOKENVALUE = "tokenValue";
    public static final String GRANT_TYPE = "grant_type";
    public static final String CLIENT_SECRET = "client_secret";
    public static final String CLIENT_ID = "client_id";
    public static final String USER_AGENT = "user_agent";
    public static final String REQUEST_IP = "request_ip";
    public static final String SESSION_ID = "session_id";
    public static final String USER_NAME = "username";
    public static final String REFRESH_TOKEN = "refresh_token";
    public static final String PASSWORD = "password";

    public static final String HEADER = "header";
    public static final String KEY = "key";
    public static final String VALUE = "value";
    public static final String ENDPOINT = "endpoint";
    public static final String METHOD = "method";
    public static final String BASIC_AUTH = "basicAuthen";
    public static final String BEARER_TOKEN = "bearerToken";
    public static final String BODY = "body";
    public static final String ACCESS_TOKEN = "access_token";


    public static final String BCTONGHOP = "BCTONGHOP";
    public static final String BCTONGHOPDONVI = "BCTONGHOPDONVI";
    public static final String BCTONGHOPDICHVU = "BCTONGHOPDICHVU";
    public static final String BCCHITIET = "BCCHITIET";
    public static final String BCCHITIETDONVI = "BCCHITIETDONVI";
    public static final String BCCHITIETDICHVU = "BCCHITIETDICHVU";

    public static final String HETHONG_CLIENT = "01";
    public static final String HETHONG_SERVER = "02";

    public static final String GET_EDOC = "getEdoc";
    public static final String SEND_EDOC = "sendEdoc";
    public static final String BACKUP = "backup";

    public static final String DONE = "done";

    public static final String STATUS_INITIAL = "Initial";
    public static final String STATUS_PROCESSING = "Processing";
    public static final String STATUS_DONE = "Done";
    public static final String STATUS_FAIL = "Fail";

    public static final String X_LGSP_FROM = "X-lgsp-from";
    public static final String X_LGSP_TO = "X-lgsp-to";
    public static final String FROM = "from";
    public static final String HEADER_MESSAGE_TYPE= "messagetype";
    public static final String HEADER_FILE_PATH = "filePath";
    public static final String HEADER_DOC_ID = "docId";
    public static final String HEADER_SERVICE_TYPE = "servicetype";

    public static final String MESSAGE_TYPE_EDOC = "edoc";
    public static final String MESSAGE_TYPE_STATUS = "status";

    public static final String CODE_OKE = "TIB_200";
    public static final String MESSAGE_OKE = "OK";

    public static final String LOCATE_FROM = "From";
    public static final String LOCATE_TO = "To";


    public static final int RUN_GET_LIST_EDOC = 4;
    public static final int RUN_GET_LIST_SENT_EDOC = 5;
    public static final int RUN_GET_EDOC = 6;
    public static final int RUN_SEND_EDOC = 7;
    public static final int RUN_UPDATE_STATUS = 8;
    public static final int RUN_GET_AGENCY_LIST = 1;
    public static final int RUN_REGISTER_AGENCY = 2;

    public static final String CHUA_GUI_THONG_BAO = "1";
    public static final String DA_GUI_THONG_BAO = "2";

    public static final String DA_DOC = "03";

    public static final String TRUC_TICH_HOP_VAN_BAN_DEFAULT = "TrucTichHopVanBan";
    public static final String DONVILIENTHONG_DOCUMENT_NOT_FOUND = "donvilienthong.document.not_found";
}
