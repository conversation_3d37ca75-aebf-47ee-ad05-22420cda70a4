package com.example.integration.constant;

public class ApiConstant {
    public static final String MESSAGE_DATA = "messageData";
    public static final String LIST_IP_SUSPECT = "listIpSuspect";
    public static final String JSON_INPUT_PARAM = "${jsonInput}";
    public static final String BLACK_LIST_PARAM = "${blackList}";
    public static final String IP_BLACK_PARAM = "${IPBlack}";
    public static final String TIME_FROM_PARAM = "${timeFrom}";
    public static final String SORT_BY_PARAM = "${sortByParam}";
    public static final String SORT_ORDER_PARAM = "${sortOrderParam}";
    public static final String LIMIT_PARAM = "${limitParam}";
    public static final String OFFSET_PARAM = "${offsetParam}";
    public static final String SUBSCRIPTION_NAME = "${subscriptionName}";
    public static final String AUTHORIZATION = "Authorization";
    public static final String BEARER = "Bearer";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String RESPONSE_CODE = "responseCode";
    public static final String CONTENT = "content";
    public static final String PASSWORD = "password";
    public static final String APIKEY = "apikey";
    public static final String POST = "post";
    public static final String PUT = "put";
    public static final String DELETE = "delete";
    public static final String DATA_SOURCE = "dataSource";
    public static final String SERVICE_HEALTH_CHECK = "serviceHealthCheck";
    public static final String NOTIFICATION_RULE = "notificationRule";
    public static final String STATISTIC_RULE = "statisticRule";
    public static final String HITS = "hits";
    public static final String LIST = "list";
    public static final String TOTAL = "total";
    public static final String VALUE = "value";
    public static final String _SOURCE = "_source";
    public static final String JSON_DATA = "json_data";
    public static final String TIME_STAMP = "@timestamp";
    public static final String PROXY_RESPONSE_CODE = "proxyResponseCode";
    public static final String RESPONSE_LATENCY = "responseLatency";
    public static final String BACKEND_LATENCY = "backendLatency";
    public static final String API_RESOURCE_TEMPLATE = "apiResourceTemplate";
    public static final String API_METHOD = "apiMethod";
    public static final String API_ID = "${apiId}";
    public static final String API_VERSION = "apiVersion";
    public static final String API_TYPE = "apiType";
    public static final String API_NAME = "apiName";
    public static final String KEY_TYPE = "${keyType}";
    public static final String SUPPORTED_GRANT_TYPES = "${supportedGrantTypes}";
    public static final String KEYMAPPING_ID = "${keyMappingId}";
    public static final String CONSUMER_KEY = "${consumerKey}";
    public static final String CONSUMER_SECRET = "${consumerSecret}";
    public static final String USER_IP = "userIp";
    public static final String APPLICATION_ID = "${applicationId}";
    public static final String VALIDITY_PERIOD = "${validityPeriod}";
    public static final String APPLICATION_NAME = "applicationName";
    public static final String TARGET_RESPONSE_CODE = "targetResponseCode";
    public static final String ERROR_MESSAGE = "errorMessage";
    public static final String REQUEST_TIMESTAMP = "requestTimestamp";
    public static final String ID = "_id";

    public static final String SUCCESS = "SUCCESS";
    public static final String FAIL    = "FAIL";
    public static final String NAME    = "${name}";
    public static final String THROTTLING_POLICY    = "${throttlingPolicy}";
    public static final String DESCRIPTION    = "${description}";

}
