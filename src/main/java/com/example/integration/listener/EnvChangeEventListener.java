package com.example.integration.listener;

import com.example.integration.config.PropKey;
import com.example.integration.constant.DBConstant;
//import com.example.integration.hub.scheduler.GetAgencyScheduler;
import com.example.integration.hub.scheduler.TaskSchedulingService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.utility.string.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
public class EnvChangeEventListener implements MessageListener {

    @Autowired
    @Lazy
    private TaskSchedulingService taskSchedulingService;

//    @Autowired
//    @Lazy
//    private GetAgencyScheduler getAgencyScheduler;


    @Override
    public void onMessage(Message message, byte[] pattern) {
        System.out.println("onMessage...EnvChangeEventListener");
        Map<String, Object> distributedProps = new HashMap<>(PropKey.getKeyMap());
        String extPropsPath = GetterUtil.get(
                distributedProps.get(PropKey.FLEXCORE_PORTAL_DISTRIBUTED_CACHE_PROPERTY_FILE_PATH), StringPool.BLANK).replace("file:", StringPool.BLANK);
        String value = new String(message.getBody(), StandardCharsets.UTF_8);
        String channel = new String(message.getChannel(), StandardCharsets.UTF_8);
        if (channel.equals(DBConstant.C_THAM_SO_CAU_HINH)) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(value);

                Properties properties = new Properties();
                Iterator<Map.Entry<String, JsonNode>> fields = jsonNode.fields();

                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> entry = fields.next();
                    if (entry.getKey().startsWith("integration.hub")) {
                        properties.setProperty(entry.getKey(), entry.getValue().asText());
                        PropKey.getKeyMap().put(entry.getKey(), entry.getValue().asText());
                    }
                }
                try (FileOutputStream fos = new FileOutputStream(extPropsPath)) {
                    properties.store(fos, "Generated from JSON");
                } catch (IOException e) {
                    log.error("Error writing properties to file: " + e.getMessage(), e);
                }
            } catch (IOException e) {
                log.error("Error parsing JSON message: " + e.getMessage(), e);
            }

//            String oldCrontab = GetterUtil.get(distributedProps.get(PropKey.INTEGRATION_HUB_SCHEDULER_GET_AGENCY_CRON), "0 0 2 * * *");
//            String newCrontab = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_SCHEDULER_GET_AGENCY_CRON), "0 0 2 * * *");
//            if (!oldCrontab.equals(newCrontab)) {
//                log.info("Update new Crontab : " + newCrontab);
//                Runnable task = () -> getAgencyScheduler.scheduleGetListAgencies();
//                taskSchedulingService.scheduleATask(PropKey.INTEGRATION_HUB_SCHEDULER_GET_AGENCY_CRON, task, newCrontab);
//                log.info("Done new Crontab : " + newCrontab);
//            }
        }

    }
}

