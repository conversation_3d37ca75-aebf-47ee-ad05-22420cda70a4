//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.vpcp.services;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.vpcp.services.model.GetChangeStatusResult;
import com.vpcp.services.model.GetEdocResult;
import com.vpcp.services.model.GetReceivedEdocResult;
import com.vpcp.services.model.GetSendEdocResult;
import com.vpcp.services.model.Knobstick;
import com.vpcp.services.model.SendEdocResult;
import com.vpcp.services.model.StatusResult;
import com.vpcp.services.utils.FileUtil;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class KnobstickServiceImp extends VnptService implements KnobstickService {
    public KnobstickServiceImp(VnptProperties properties) {
        super(properties);
    }

    public GetReceivedEdocResult getReceivedEdocList(String jsonHeaders) {
        String urlRequest = this.endpoint + "/getReceivedEdocList";
        this.headers.put("Content-Type", "application/json");
        GetReceivedEdocResult receivedEdocResult = null;

        try {
            JsonParser parser = new JsonParser();
            JsonElement element = parser.parse(jsonHeaders);

            for(Map.Entry<String, JsonElement> entry : element.getAsJsonObject().entrySet()) {
                this.headers.put((String)entry.getKey(), ((JsonElement)entry.getValue()).getAsString());
            }

            String json = this.execute(urlRequest, "POST", (HashMap)null, this.getListHeader());
            parser = new JsonParser();
            element = parser.parse(json);
            if (element != null) {
                receivedEdocResult = new GetReceivedEdocResult();
                String status = element.getAsJsonObject().get("status").getAsString();
                String errorCode = element.getAsJsonObject().get("ErrorCode").getAsString();
                String errorDesc = element.getAsJsonObject().get("ErrorDesc").getAsString();
                receivedEdocResult.setErrorCode(errorCode);
                receivedEdocResult.setErrorDesc(errorDesc);
                receivedEdocResult.setStatus(status);
                if ("0".equalsIgnoreCase(errorCode)) {
                    JsonArray data = element.getAsJsonObject().get("data").getAsJsonArray();
                    List<Knobstick> knobsticks = new ArrayList();
                    if (data != null) {
                        for(JsonElement elementData : data) {
                            Knobstick knobstick = new Knobstick();
                            if (elementData.getAsJsonObject().get("docId") != null) {
                                knobstick.setId(elementData.getAsJsonObject().get("docId").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("serviceType") != null) {
                                knobstick.setServiceType(elementData.getAsJsonObject().get("serviceType").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("messageType") != null) {
                                knobstick.setMessageType(elementData.getAsJsonObject().get("messageType").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("created_time") != null) {
                                knobstick.setCreatedTime(elementData.getAsJsonObject().get("created_time").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("updated_time") != null) {
                                knobstick.setUpdatedTime(elementData.getAsJsonObject().get("updated_time").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("from") != null) {
                                knobstick.setFrom(elementData.getAsJsonObject().get("from").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("to") != null) {
                                knobstick.setTo(elementData.getAsJsonObject().get("to").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("status") != null) {
                                knobstick.setStatus(elementData.getAsJsonObject().get("status").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("status_desc") != null) {
                                knobstick.setStatusDesc(elementData.getAsJsonObject().get("status_desc").getAsString());
                            }

                            knobsticks.add(knobstick);
                        }
                    }

                    receivedEdocResult.setKnobsticks(knobsticks);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return receivedEdocResult;
    }

    public GetSendEdocResult getSentEdocList(String jsonHeaders) {
        this.headers.put("Content-Type", "application/json");
        String urlRequest = this.endpoint + "/getSentEdocList";
        GetSendEdocResult getSendEdocResult = null;

        try {
            JsonParser parser = new JsonParser();
            JsonElement element = parser.parse(jsonHeaders);

            for(Map.Entry<String, JsonElement> entry : element.getAsJsonObject().entrySet()) {
                this.headers.put((String)entry.getKey(), ((JsonElement)entry.getValue()).getAsString());
            }

            String json = this.execute(urlRequest, "POST", (HashMap)null, this.getListHeader());
            parser = new JsonParser();
            element = parser.parse(json);
            if (element != null) {
                getSendEdocResult = new GetSendEdocResult();
                String status = element.getAsJsonObject().get("status").getAsString();
                String errorCode = element.getAsJsonObject().get("ErrorCode").getAsString();
                String errorDesc = element.getAsJsonObject().get("ErrorDesc").getAsString();
                getSendEdocResult.setErrorCode(errorCode);
                getSendEdocResult.setErrorDesc(errorDesc);
                getSendEdocResult.setStatus(status);
                if ("0".equalsIgnoreCase(errorCode)) {
                    JsonArray data = element.getAsJsonObject().get("data").getAsJsonArray();
                    List<StatusResult> statusResults = new ArrayList();
                    if (data != null) {
                        for(JsonElement elementData : data) {
                            StatusResult statusResult = new StatusResult();
                            if (elementData.getAsJsonObject().get("serviceType") != null) {
                                statusResult.setServiceType(elementData.getAsJsonObject().get("serviceType").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("receiverDocId") != null) {
                                statusResult.setReceiverDocId(elementData.getAsJsonObject().get("receiverDocId").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("toCode") != null) {
                                statusResult.setToCode(elementData.getAsJsonObject().get("toCode").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("receiveStatus") != null) {
                                statusResult.setReceiveStatus(elementData.getAsJsonObject().get("receiveStatus").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("title") != null) {
                                statusResult.setTitle(elementData.getAsJsonObject().get("title").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("messageType") != null) {
                                statusResult.setMessageType(elementData.getAsJsonObject().get("messageType").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("notation") != null) {
                                statusResult.setNotation(elementData.getAsJsonObject().get("notation").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("receivedTime") != null) {
                                statusResult.setReceivedTime(elementData.getAsJsonObject().get("receivedTime").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("sentTime") != null) {
                                statusResult.setSentTime(elementData.getAsJsonObject().get("sentTime").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("sendStatus") != null) {
                                statusResult.setSendStatus(elementData.getAsJsonObject().get("sendStatus").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("sendStatusDesc") != null) {
                                statusResult.setSendStatusDesc(elementData.getAsJsonObject().get("sendStatusDesc").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("senderDocId") != null) {
                                statusResult.setSenderDocId(elementData.getAsJsonObject().get("senderDocId").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("fromCode") != null) {
                                statusResult.setFromCode(elementData.getAsJsonObject().get("fromCode").getAsString());
                            }

                            if (elementData.getAsJsonObject().get("receiveStatusDesc") != null) {
                                statusResult.setReceiveStatusDesc(elementData.getAsJsonObject().get("receiveStatusDesc").getAsString());
                            }

                            statusResults.add(statusResult);
                        }
                    }

                    getSendEdocResult.setStatusResult(statusResults);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return getSendEdocResult;
    }

    public SendEdocResult sendEdoc(String jsonHeaders, String edXMLFileLocation) {
        String urlRequest = this.endpoint + "/sendEdoc";
        SendEdocResult sendEdocResult = null;

        try {
            this.headers.put("Content-Type", "application/octet-stream");
            JsonParser parser = new JsonParser();
            JsonElement element = parser.parse(jsonHeaders);

            for(Map.Entry<String, JsonElement> entry : element.getAsJsonObject().entrySet()) {
                if (((String)entry.getKey()).equals("to")) {
                    String s = ((JsonElement)entry.getValue()).toString();
                    this.headers.put((String)entry.getKey(), s);
                } else {
                    this.headers.put((String)entry.getKey(), ((JsonElement)entry.getValue()).getAsString());
                }
            }

            this.createContentHA256(edXMLFileLocation);
            FileInputStream inputStream = new FileInputStream(edXMLFileLocation);
            String json = this.executeSendFileDoc(urlRequest, "POST", (HashMap)null, this.getListHeader(), inputStream);
            parser = new JsonParser();
            element = parser.parse(json);
            System.out.println(json);
            if (element != null) {
                sendEdocResult = new SendEdocResult();
                String status = element.getAsJsonObject().get("status").getAsString();
                String errorCode = element.getAsJsonObject().get("ErrorCode").getAsString();
                String errorDesc = element.getAsJsonObject().get("ErrorDesc").getAsString();
                String docID = element.getAsJsonObject().get("DocId").getAsString();
                sendEdocResult.setErrorCode(errorCode);
                sendEdocResult.setErrorDesc(errorDesc);
                sendEdocResult.setStatus(status);
                sendEdocResult.setDocID(docID);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return sendEdocResult;
    }

    public GetEdocResult getEdoc(String jsonHeaders) {
        String urlRequest = this.endpoint + "/getEdoc";
        GetEdocResult getEdocResult = null;
        String filePath = "";
        String docId = "";

        try {
            this.headers.put("Content-Type", "application/json");
            JsonParser parser = new JsonParser();
            JsonElement element = parser.parse(jsonHeaders);

            for(Map.Entry<String, JsonElement> entry : element.getAsJsonObject().entrySet()) {
                this.headers.put((String)entry.getKey(), ((JsonElement)entry.getValue()).getAsString());
                if ("filePath".equalsIgnoreCase((String)entry.getKey())) {
                    filePath = ((JsonElement)entry.getValue()).getAsString();
                }

                if ("docId".equalsIgnoreCase((String)entry.getKey())) {
                    docId = ((JsonElement)entry.getValue()).getAsString();
                }
            }

            //Lấy edoc bằng phương thức mới, tránh làm spike ram,cpu - giaonn was here
            int year = Calendar.getInstance().get(1);
            int month = Calendar.getInstance().get(2) + 1;
            filePath = filePath + "/" + year + "/" + month;
            if (!FileUtil.exist(filePath)) {
                FileUtil.mkdirs(filePath);
            }

            String out = filePath + "/" + docId + ".txt";
            getEdocResult = this.execute(urlRequest, out);


            //Phương thức cũ lởm như chó
//            String json = this.execute(urlRequest, "POST", (HashMap)null, this.getListHeader(), inputStream);
//            parser = new JsonParser();
//            element = parser.parse(json);
//            if (element != null) {
//                getEdocResult = new GetEdocResult();
//                String status = element.getAsJsonObject().get("status").getAsString();
//                String errorCode = element.getAsJsonObject().get("ErrorCode").getAsString();
//                String errorDesc = element.getAsJsonObject().get("ErrorDesc").getAsString();
//                Base64 base64 = new Base64();
//
//
//                String data = new String(base64.decode(element.getAsJsonObject().get("data").getAsString()), Charset.forName("UTF-8"));
//                getEdocResult.setErrorCode(errorCode);
//                getEdocResult.setErrorDesc(errorDesc);
//                getEdocResult.setStatus(status);
//                getEdocResult.setData(data);
//                if (!StringUtils.isNullOrEmpty(filePath) && "0".equalsIgnoreCase(errorCode)) {
//                    int year = Calendar.getInstance().get(1);
//                    int month = Calendar.getInstance().get(2) + 1;
//                    filePath = filePath + "/" + year + "/" + month;
//                    if (!FileUtil.exist(filePath)) {
//                        FileUtil.mkdirs(filePath);
//                    }
//
//                    filePath = filePath + "/" + docId + ".edxml";
//                    Files.write(data.getBytes("UTF-8"), new File(filePath));
//                    getEdocResult.setFilePath(filePath);
//                }
//            }
        } catch (Exception e) {
            System.out.println("Error getEdoc111: " + e.getMessage());
            e.printStackTrace();
        }

        return getEdocResult;
    }

    public GetChangeStatusResult updateStatus(String jsonHeaders) {
        this.headers.put("Content-Type", "application/json");
        String urlRequest = this.endpoint + "/updateStatus";
        GetChangeStatusResult getEdocResult = null;
        FileInputStream inputStream = null;

        try {
            JsonParser parser = new JsonParser();
            JsonElement element = parser.parse(jsonHeaders);

            for(Map.Entry<String, JsonElement> entry : element.getAsJsonObject().entrySet()) {
                this.headers.put((String)entry.getKey(), ((JsonElement)entry.getValue()).getAsString());
            }

            String json = this.execute(urlRequest, "POST", (HashMap)null, this.getListHeader(), inputStream);
            parser = new JsonParser();
            element = parser.parse(json);
            if (element != null) {
                getEdocResult = new GetChangeStatusResult();
                String status = element.getAsJsonObject().get("status").getAsString();
                String errorCode = element.getAsJsonObject().get("ErrorCode").getAsString();
                String errorDesc = element.getAsJsonObject().get("ErrorDesc").getAsString();
                getEdocResult.setErrorCode(errorCode);
                getEdocResult.setErrorDesc(errorDesc);
                getEdocResult.setStatus(status);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return getEdocResult;
    }

    private void createContentHA256(String edXMLFileLocation) {
        FileInputStream fis = null;

        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            fis = new FileInputStream(edXMLFileLocation);
            byte[] dataBytes = new byte[1024];
            int nread = 0;

            while((nread = fis.read(dataBytes)) != -1) {
                md.update(dataBytes, 0, nread);
            }

            byte[] mdbytes = md.digest();
            StringBuffer sb = new StringBuffer();

            for(int i = 0; i < mdbytes.length; ++i) {
                sb.append(Integer.toString((mdbytes[i] & 255) + 256, 16).substring(1));
            }

            System.out.println("Hex format : " + sb.toString());
            StringBuffer hexString = new StringBuffer();

            for(int i = 0; i < mdbytes.length; ++i) {
                hexString.append(Integer.toHexString(255 & mdbytes[i]));
            }

            this.headers.put("hash-file", hexString.toString());
            System.out.println("Hex format : " + hexString.toString());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

    }

    public static void main(String[] args) {
        FileConfig.setFileConfig("D:/Works/Thangtt/Java/services/resources/collaboration.properties");
        VnptProperties vnptProperties = new VnptProperties(FileConfig.getCollaborationPF());
        KnobstickServiceImp knobstickServiceImp = new KnobstickServiceImp(vnptProperties);
        knobstickServiceImp.getReceivedEdocList("{}");
    }
}
