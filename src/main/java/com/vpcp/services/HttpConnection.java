//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.vpcp.services;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.fasterxml.jackson.core.Base64Variants;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.vpcp.services.model.GetEdocResult;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;

public class HttpConnection {
    private String url;
    private String method;
    private VnptProperties properties;

    public HttpConnection(VnptProperties properties, String url, String method) {
        this.properties = properties;
        this.url = url;
        this.method = method;
    }

    protected String sendGet(HashMap<String, String> paras, List<Header> headers) throws Exception {
        HttpClientFactory factory = new HttpClientFactory(this.properties);
        HttpClient client = factory.getHttpsClient(headers);
        URIBuilder builder = new URIBuilder(this.url);
        if (paras != null) {
            for(String key : paras.keySet()) {
                System.out.println((String)paras.get(key));
                builder.setParameter(key, (String)paras.get(key));
            }
        }

        URI uri = builder.build();
        HttpGet httpget = new HttpGet(uri);
        System.out.println(httpget.getURI());
        HttpGet request = new HttpGet(httpget.getURI());
        HttpResponse response = client.execute(request);
        System.out.println("\nSending 'GET' request to URL : " + this.url);
        System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
        BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
        StringBuffer result = new StringBuffer();
        String line = "";

        while((line = rd.readLine()) != null) {
            result.append(line);
        }

        return result.toString();
    }

    public String sendPost(HashMap<String, String> paras, List<Header> headers) throws Exception {
        HttpClientFactory factory = new HttpClientFactory(this.properties);
        HttpClient client = factory.getHttpsClient(headers);
        StringBuffer result = new StringBuffer();
        HttpPost post = new HttpPost(this.url);
        List<NameValuePair> urlParameters = new ArrayList();
        if (paras != null) {
            for(String key : paras.keySet()) {
                urlParameters.add(new BasicNameValuePair(key, (String)paras.get(key)));
            }
        }

        try {
            post.setEntity(new UrlEncodedFormEntity(urlParameters));
            HttpResponse response = client.execute(post);
            System.out.println("\nSending 'POST' request to URL : " + this.url);
            System.out.println("-->Post parameters : " + post.getEntity());
            System.out.println("-->Response Code : " + response.getStatusLine().getStatusCode());
            BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
            String line = "";

            while((line = rd.readLine()) != null) {
                result.append(line);
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return result.toString();
    }

    private String sendPostMultipartFileDoc(HashMap<String, String> paras, List<Header> headers, InputStream content) throws Exception {
        String charset = "UTF-8";
        URIBuilder uri = new URIBuilder(this.url);
        if (paras != null) {
            for(String key : paras.keySet()) {
                uri.setParameter(key, (String)paras.get(key));
            }
        }

        MultipartUtility multipart = new MultipartUtility(uri.toString(), charset, headers);
        multipart.addInputStreamPart("file_doc", content);
        String response = multipart.finish();
        System.out.println("response: " + response);
        return response;
    }


    public String sendPostMultipart(HashMap<String, String> paras, List<Header> headers, InputStream content) throws Exception {
        HttpClientFactory factory = new HttpClientFactory(this.properties);
        CloseableHttpClient client = (CloseableHttpClient)factory.getHttpsClient(headers);
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        URIBuilder uri = new URIBuilder(this.url);
        if (paras != null) {
            for(String key : paras.keySet()) {
                uri.setParameter(key, (String)paras.get(key));
            }
        }

        StringEntity stringEntity = new StringEntity("{}");
        HttpPost httpPost = new HttpPost(uri.build());
        httpPost.setEntity(stringEntity);
        HttpResponse response = client.execute(httpPost);
        System.out.println("\nSending 'POST' request to URL : " + this.url);
        System.out.println("Post  parameters : " + response.getEntity());
        System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
        BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
        StringBuffer result = new StringBuffer();
        String line = "";

        while((line = rd.readLine()) != null) {
            result.append(line);
        }

        client.close();
        return result.toString();
    }

    public String sendPostMultipart(HashMap<String, String> paras, List<Header> headers, String content) throws Exception {
        HttpClientFactory factory = new HttpClientFactory(this.properties);
        CloseableHttpClient client = (CloseableHttpClient)factory.getHttpsClient(headers);
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        URIBuilder uri = new URIBuilder(this.url);
        if (paras != null) {
            for(String key : paras.keySet()) {
                uri.setParameter(key, (String)paras.get(key));
            }
        }

        if (content != null) {
            multipartEntityBuilder.addBinaryBody("content", content.getBytes());
        }

        HttpEntity entity = multipartEntityBuilder.build();
        HttpPost httpPost = new HttpPost(uri.build());
        httpPost.setEntity(entity);
        HttpResponse response = client.execute(httpPost);
        System.out.println("\nSending 'POST' request to URL : " + this.url);
        System.out.println("Post parameters : " + response.getEntity());
        System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
        BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
        StringBuffer result = new StringBuffer();
        String line = "";

        while((line = rd.readLine()) != null) {
            result.append(line);
        }

        client.close();
        return result.toString();
    }

    public String sendPostJson(HashMap<String, String> paras, List<Header> headers, String json) throws Exception {
        HttpClientFactory factory = new HttpClientFactory(this.properties);
        CloseableHttpClient client = (CloseableHttpClient)factory.getHttpsClient(headers);
        URIBuilder uri = new URIBuilder(this.url);
        if (paras != null) {
            for(String key : paras.keySet()) {
                uri.setParameter(key, (String)paras.get(key));
            }
        }

        HttpPost httpPost = new HttpPost(uri.build());
        httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        HttpResponse response = client.execute(httpPost);
        System.out.println("\nSending 'POST' request to URL : " + this.url);
        System.out.println("Post parameters : " + response.getEntity());
        System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
        BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
        StringBuffer result = new StringBuffer();
        String line = "";

        while((line = rd.readLine()) != null) {
            result.append(line);
        }

        client.close();
        return result.toString();
    }

    public String execute(HashMap<String, String> paras, List<Header> headers) throws Exception {
        String result = "";
        if ("GET".equals(this.getMethod())) {
            result = this.sendGet(paras, headers);
        } else {
            result = this.sendPost(paras, headers);
        }

        return result;
    }

    public GetEdocResult execute(List<Header> headers, String filePath) {
        long startTime = System.currentTimeMillis(); // 🕒 Bắt đầu đo thời gian

        GetEdocResult edocResult = new GetEdocResult();
        try {
            URL url = new URL(this.url);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");

            for (Header h : headers) {
                if (!"XroadVnpt-docid".equals(h.getName())) {
                    conn.setRequestProperty(h.getName(), h.getValue());
                }
            }

            conn.setConnectTimeout(10_000);
            conn.setReadTimeout(2 * 60 * 1000);
            conn.setDoOutput(true);

            String jsonPayload = "{}";
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                os.write(input);
                os.flush();
            }

            try (InputStream in = conn.getInputStream();
                 FileOutputStream fos = new FileOutputStream(filePath);
                 BufferedOutputStream bos = new BufferedOutputStream(fos)) {

                byte[] buffer = new byte[8 * 1024]; // 64KB
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    bos.write(buffer, 0, bytesRead);
                }

                bos.flush();
                edocResult.setFilePath(filePath);

            } catch (IOException e) {
                System.out.println("Error when call getEdoc path: " + filePath);
                e.printStackTrace();
                edocResult.setStatus("FAIL");
                edocResult.setErrorCode("-1");
                edocResult.setErrorDesc("Error while reading file eDoc");
            }

        } catch (Exception e) {
            System.out.println("Error create connection getEdoc: " + filePath);
            e.printStackTrace();
            edocResult.setStatus("FAIL");
            edocResult.setErrorCode("-1");
            edocResult.setErrorDesc("Error create connection getEdoc");
        }

        long endTime = System.currentTimeMillis(); // 🕒 Kết thúc đo
        long elapsedTime = endTime - startTime;
        System.out.println(">>> [INFO] execute() completed in " + elapsedTime + " ms");

        return edocResult;
    }


    private GetEdocResult executeCooked(List<Header> headers, String filePath) {
        //Call API getEdoc from QuocGia, decode and save to file
        GetEdocResult edocResult = new GetEdocResult();
        try {
            URL url = new URL(this.url);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");

            for (Header h : headers) {
                if (h.getName().equals("XroadVnpt-docid")) {
                    continue;
                }
                conn.setRequestProperty(h.getName(), h.getValue());
            }
            System.out.println("No more read timeout");
            conn.setConnectTimeout(10_000);
//            conn.setReadTimeout(50_000);
            conn.setDoOutput(true);

            String jsonPayload = "{}";
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
                os.flush();
            }

            try (InputStream in = conn.getInputStream();
                 FileOutputStream fos = new FileOutputStream(filePath);
            ) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }

                fos.flush();
                edocResult.setFilePath(filePath);
            } catch (IOException e) {
                System.out.println("Error when call getEdoc path: " + filePath);
                e.printStackTrace();
                edocResult.setStatus("FAIL");
                edocResult.setErrorCode("-1");
                edocResult.setErrorDesc("Error while reading file eDoc");
            }
        } catch (Exception e) {
            System.out.println("Error create connection getEdoc: " + filePath);
            e.printStackTrace();
            edocResult.setStatus("FAIL");
            edocResult.setErrorCode("-1");
            edocResult.setErrorDesc("Error create connection getEdoc");
        }

        return edocResult;
    }

    public String execute(HashMap<String, String> paras, List<Header> headers, InputStream content) throws Exception {
        String result = "";
        result = this.sendPostMultipart(paras, headers, content);
        return result;
    }

    public String execute(HashMap<String, String> paras, List<Header> headers, String content) throws Exception {
        String result = "";
        result = this.sendPostMultipart(paras, headers, content);
        return result;
    }

    public String executeSendFileDoc(HashMap<String, String> paras, List<Header> headers, InputStream content) throws Exception {
        String result = "";
        result = this.sendPostMultipartFileDoc(paras, headers, content);
        return result;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMethod() {
        return this.method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
}
