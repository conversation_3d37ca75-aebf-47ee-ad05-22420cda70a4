//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.vpcp.services;

import com.google.gson.JsonObject;
import com.vpcp.services.exception.ConfigureException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.vpcp.services.model.GetEdocResult;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;

public abstract class VnptService {
    protected String authorization = "";
    protected String user_Agent = "JAVASDKAgent/1.0.0 XRoad/VPCP";
    protected String version = "3.0.201608";
    protected String accept = "application/xml";
    protected String content_Type = "application/json";
    protected String protocol = "HTTP";
    protected String application = "";
    protected String endpoint = "";
    protected String storePathDir = "";
    protected String systemId = "";
    protected String secret = "";
    protected HashMap<String, String> headers;
    HttpConnection httpConnection;
    protected VnptProperties properties;

    public VnptService(VnptProperties properties) {
        if (properties == null) {
            throw new ConfigureException("VnptProperties must initialization");
        } else {
            this.headers = new HashMap();
            Date d = new Date();
            this.headers.put("timestamp", (new SimpleDateFormat("ddMMyyyy'T'HHmmss+SSSS")).format(d));
            this.properties = properties;
            this.init();
        }
    }

    private void init() {
        if (this.properties != null) {
            this.protocol = this.properties.getProtocol();
            this.endpoint = this.properties.getEndpoint();
            this.storePathDir = this.properties.getStorePathDir();
            this.systemId = this.properties.getSystemId();
            this.secret = this.properties.getSecret();
            this.headers.put("systemid", this.systemId);
        }

    }

    private void createHttpConnection(String urlRequest, String method) {
        this.httpConnection = new HttpConnection(this.properties, urlRequest, method);
    }

    public String execute(String urlRequest, String method, HashMap<String, String> paras, List<Header> headers) throws Exception {
        this.createHttpConnection(urlRequest, method);
        String content = this.httpConnection.execute(paras, this.addXroadVnptHashHeader());
        return content;
    }

    public GetEdocResult execute(String urlRequest, String filePath) throws Exception {
        this.createHttpConnection(urlRequest, "POST");
        return this.httpConnection.execute(this.addXroadVnptHashHeader(), filePath);
    }

    public String execute(String urlRequest, String method, HashMap<String, String> paras, List<Header> headers, InputStream content) throws Exception {
        this.createHttpConnection(urlRequest, method);
        String result = this.httpConnection.execute(paras, this.addXroadVnptHashHeader(), content);
        return result;
    }

    public String execute(String urlRequest, String method, HashMap<String, String> paras, List<Header> headers, String content) throws Exception {
        this.createHttpConnection(urlRequest, method);
        String result = this.httpConnection.execute(paras, this.addXroadVnptHashHeader(), content);
        return result;
    }

    public String executeSendFileDoc(String urlRequest, String method, HashMap<String, String> paras, List<Header> headers, InputStream content) throws Exception {
        this.createHttpConnection(urlRequest, method);
        String result = this.httpConnection.executeSendFileDoc(paras, this.addXroadVnptHashHeader(), content);
        return result;
    }

    public String executeJson(String urlRequest, String method, HashMap<String, String> paras, List<Header> headers, String content) throws Exception {
        this.createHttpConnection(urlRequest, method);
        String result = this.httpConnection.sendPostJson(paras, this.addXroadVnptHashHeader(), content);
        return result;
    }

    public List<Header> getListHeader() {
        List<Header> liHeaders = new ArrayList();

        for(String key : this.headers.keySet()) {
            String value = (String)this.headers.get(key);
            liHeaders.add(new BasicHeader(key, value));
        }

        return liHeaders;
    }

    private List<Header> addXroadVnptHashHeader() {
        List<Header> tmp = new ArrayList();
        String vnptHeader = "";
        String vnptHeaderKey = "";
        JsonObject jsonObject = new JsonObject();

        for(Header header : this.getListHeader()) {
            if (header.getName().equals("Content-Type")) {
                tmp.add(new BasicHeader(header.getName(), header.getValue()));
            }

            jsonObject.addProperty("XroadVnpt-" + header.getName(), header.getValue());
            tmp.add(new BasicHeader("XroadVnpt-" + header.getName(), header.getValue()));
            vnptHeaderKey = vnptHeaderKey + header.getName() + ";";
        }

        if (vnptHeaderKey != null && vnptHeaderKey.endsWith(";")) {
            vnptHeaderKey = vnptHeaderKey.substring(0, vnptHeaderKey.length() - 1);
        }

        vnptHeader = jsonObject.toString();
        System.out.println(vnptHeader);

        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(this.secret.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Hex.encodeHexString(sha256_HMAC.doFinal(vnptHeader.getBytes("UTF-8")));
            System.out.println(hash);
            tmp.add(new BasicHeader("XroadVnpt-sign-header-key", vnptHeaderKey));
            tmp.add(new BasicHeader("XroadVnpt-sign-header", vnptHeader));
            tmp.add(new BasicHeader("XroadVnpt-hash-header", hash));
            tmp.add(new BasicHeader("User-Agent", this.user_Agent));
            tmp.add(new BasicHeader("XroadVnpt-version", this.version));
            return tmp;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }

        return null;
    }
}
