{"info": {"name": "TrucTichHopController API Collection", "description": "Collection of API endpoints for the TrucTichHopController in the Integration Hub application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create TrucTichHop", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"MaMuc\": \"TRUC_TICH_HOP_01\",\n  \"TenMuc\": \"<PERSON><PERSON><PERSON><PERSON> tích hợp văn bản\",\n  \"DinhDangGoiTin\": \"JSON\",\n  \"CauHinhGoiTin\": [\n    {\n      \"KieuLoaiGoiTin\": \"SEND_EDOC\",\n      \"TenKieuLoaiGoiTin\": \"Gửi văn bản điện tử\",\n      \"ThongSoKyThuat\": []\n    },\n    {\n      \"KieuLoaiGoiTin\": \"RECEIVE_EDOC\",\n      \"TenKieuLoaiGoiTin\": \"Nhận văn bản điện tử\",\n      \"ThongSoKyThuat\": []\n    }\n  ],\n  \"HeThongKetNoi\": [\n    {\n      \"MaKetNoi\": \"G01\",\n      \"TenKetNoi\": \"Văn phòng Chính phủ\",\n      \"GiaoThucKetNoi\": {\n        \"MaMuc\": \"REST\",\n        \"TenMuc\": \"RESTful API\"\n      },\n      \"CauHinhKetNoi\": \"{\\\"url\\\":\\\"http://example.com/api\\\",\\\"method\\\":\\\"POST\\\"}\",\n      \"ChuoiMauNoiNhan\": \"G01\",\n      \"TanSuatQuet\": 300000,\n      \"ThoiGianHieuLuc\": 0,\n      \"Email\": \"<EMAIL>\",\n      \"Telegram\": \"@example_bot\",\n      \"TeleNotification\": {\n        \"isShowVanBanTonTime\": true,\n        \"isShowVanBanTonUnit\": true,\n        \"isShowVanBanLoiTime\": true,\n        \"isShowVanBanLoiUnit\": true,\n        \"isShowVanBanLateTime\": true,\n        \"isShowVanBanLateUnit\": true,\n        \"isShowVanHanhTrucTime\": true,\n        \"isShowVanHanhTrucUnit\": true,\n        \"isShowVanHanhTrucType\": true\n      },\n      \"EmailNotification\": {\n        \"isShowVanBanTonTime\": true,\n        \"isShowVanBanTonUnit\": true,\n        \"isShowVanBanLoiTime\": true,\n        \"isShowVanBanLoiUnit\": true,\n        \"isShowVanBanLateTime\": true,\n        \"isShowVanBanLateUnit\": true,\n        \"isShowVanHanhTrucTime\": true,\n        \"isShowVanHanhTrucUnit\": true,\n        \"isShowVanHanhTrucType\": true\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/integration/1.0/tructichhop", "host": ["{{baseUrl}}"], "path": ["integration", "1.0", "tructichhop"]}, "description": "Create a new TrucTichHop (Integration Channel) with the specified configuration."}, "response": []}, {"name": "Update TrucTichHop", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"MaMuc\": \"TRUC_TICH_HOP_01\",\n  \"TenMuc\": \"<PERSON><PERSON><PERSON><PERSON> tích hợp văn bản (Updated)\",\n  \"DinhDangGoiTin\": \"JSON\",\n  \"CauHinhGoiTin\": [\n    {\n      \"KieuLoaiGoiTin\": \"SEND_EDOC\",\n      \"TenKieuLoaiGoiTin\": \"Gửi văn bản điện tử\",\n      \"ThongSoKyThuat\": []\n    },\n    {\n      \"KieuLoaiGoiTin\": \"RECEIVE_EDOC\",\n      \"TenKieuLoaiGoiTin\": \"Nhận văn bản điện tử\",\n      \"ThongSoKyThuat\": []\n    }\n  ],\n  \"HeThongKetNoi\": [\n    {\n      \"MaKetNoi\": \"G01\",\n      \"TenKetNoi\": \"Văn phòng Chính phủ\",\n      \"GiaoThucKetNoi\": {\n        \"MaMuc\": \"REST\",\n        \"TenMuc\": \"RESTful API\"\n      },\n      \"CauHinhKetNoi\": \"{\\\"url\\\":\\\"http://example.com/api\\\",\\\"method\\\":\\\"POST\\\"}\",\n      \"ChuoiMauNoiNhan\": \"G01\",\n      \"TanSuatQuet\": 300000,\n      \"ThoiGianHieuLuc\": 0,\n      \"Email\": \"<EMAIL>\",\n      \"Telegram\": \"@updated_bot\",\n      \"TeleNotification\": {\n        \"isShowVanBanTonTime\": true,\n        \"isShowVanBanTonUnit\": true,\n        \"isShowVanBanLoiTime\": true,\n        \"isShowVanBanLoiUnit\": true,\n        \"isShowVanBanLateTime\": true,\n        \"isShowVanBanLateUnit\": true,\n        \"isShowVanHanhTrucTime\": true,\n        \"isShowVanHanhTrucUnit\": true,\n        \"isShowVanHanhTrucType\": true\n      },\n      \"EmailNotification\": {\n        \"isShowVanBanTonTime\": true,\n        \"isShowVanBanTonUnit\": true,\n        \"isShowVanBanLoiTime\": true,\n        \"isShowVanBanLoiUnit\": true,\n        \"isShowVanBanLateTime\": true,\n        \"isShowVanBanLateUnit\": true,\n        \"isShowVanHanhTrucTime\": true,\n        \"isShowVanHanhTrucUnit\": true,\n        \"isShowVanHanhTrucType\": true\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/integration/1.0/tructichhop/{{trucTichHopId}}", "host": ["{{baseUrl}}"], "path": ["integration", "1.0", "tructichhop", "{{trucTichHopId}}"]}, "description": "Update an existing TrucTichHop (Integration Channel) with the specified ID."}, "response": []}, {"name": "Get TrucTichHop by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/integration/1.0/tructichhop/{{trucTichHopId}}", "host": ["{{baseUrl}}"], "path": ["integration", "1.0", "tructichhop", "{{trucTichHopId}}"]}, "description": "Get a TrucTichHop (Integration Channel) by its ID."}, "response": []}, {"name": "Delete TrucTichHop", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/integration/1.0/tructichhop/{{trucTichHopId}}", "host": ["{{baseUrl}}"], "path": ["integration", "1.0", "tructichhop", "{{trucTichHopId}}"]}, "description": "Delete a TrucTichHop (Integration Channel) by its ID."}, "response": []}, {"name": "Filter T<PERSON>ich<PERSON>op", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/integration/1.0/tructichhop/filter?keyword=&page=0&size=10&orderFields=ThoiGianCapNhat&orderTypes=desc", "host": ["{{baseUrl}}"], "path": ["integration", "1.0", "tructichhop", "filter"], "query": [{"key": "keyword", "value": "", "description": "Optional search keyword"}, {"key": "page", "value": "0", "description": "Page number (zero-based)"}, {"key": "size", "value": "10", "description": "Page size"}, {"key": "orderFields", "value": "ThoiGianCapNhat", "description": "Field to order by"}, {"key": "orderTypes", "value": "desc", "description": "Order direction (asc or desc)"}]}, "description": "Filter and paginate TrucTichHop (Integration Channel) records."}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:8018", "type": "string", "description": "Base URL for the Integration Hub API"}, {"key": "trucTichHopId", "value": "your-tructichhop-id", "type": "string", "description": "ID of a TrucTichHop to use in requests"}]}